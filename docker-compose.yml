version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pixelpark-app
    env_file:
      - .env
    depends_on:
      - redis
      - mongo
      - elasticsearch
    networks:
      - pixelpark-network
    dns:
      - 8.8.8.8
      - 8.8.4.4
    ports:
      - "3001:3001"

  redis:
    image: redis:7
    container_name: pixelpark-redis
    ports:
      - "6379:6379"
    networks:
      - pixelpark-network
    # command: redis-server --requirepass ${REDIS_PASSWORD:-}
    command: [ "redis-server", "--requirepass", "pixelpark_secure_password" ]

  mongo:
    image: mongo:6
    container_name: pixelpark-mongo
    ports:
      - "27017:27017"
    networks:
      - pixelpark-network
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_INITDB_ROOT_USERNAME:-root}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_INITDB_ROOT_PASSWORD:-example}
    volumes:
      - mongo-data:/data/db

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.10
    container_name: pixelpark-elasticsearch
    environment:
      - node.name=pixelpark-es-node
      - cluster.name=pixelpark-es-cluster
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=true
      - ELASTIC_PASSWORD=pixelpark_secure_password
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
      - "9300:9300"
    networks:
      - pixelpark-network
    healthcheck:
      test: ["CMD-SHELL", "curl -s http://localhost:9200 | grep -q 'pixelpark-es-cluster'"]
      interval: 30s
      timeout: 10s
      retries: 5

  kibana:
    image: docker.elastic.co/kibana/kibana:7.17.10
    container_name: pixelpark-kibana
    environment:
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=elastic
      - ELASTICSEARCH_PASSWORD=pixelpark_secure_password
    ports:
      - "5601:5601"
    networks:
      - pixelpark-network
    depends_on:
      - elasticsearch
    healthcheck:
      test: ["CMD-SHELL", "curl -s http://localhost:5601/api/status | grep -q 'Looking good'"]
      interval: 30s
      timeout: 10s
      retries: 5

networks:
  pixelpark-network:
    driver: bridge

volumes:
  elasticsearch-data:
    driver: local
  mongo-data:
    driver: local