NODE_ENV="UAT2"
# MONGO_DB_URI="mongodb+srv://RI_PixelPark_Dev2:<EMAIL>/pixelpark_uat?retryWrites=true&w=majority"
MONGO_DB_URI="mongodb+srv://pixelpark:<EMAIL>/"

AWS_SECRET_NAME="arn:aws:secretsmanager:us-east-1:566837086641:secret:uat/pixelpark-lGDh9j"
AWS_REGION="us-east-1"
AWS_ACCESS_KEY="********************"
AWS_SECRET_KEY="RU/Bkdp6UpjgTnskNjph+JXi88XXvLdyxFxtkRt4"
PORT=3001

START_BLOCK=20700369

REDIS_PORT="6379"
REDIS_HOST="**************"
REDIS_PASSWORD="redis@12345hjk"

MULTICALL_CONTRACT_ADDRESS="0x8C69ff504272Fa9BCe1d0eb920427b28f4aC6a21"

TESTNET_RPC="https://rpc.v4.testnet.pulsechain.com"

MAINNET_RPC="https://pulsechain-rpc.publicnode.com"