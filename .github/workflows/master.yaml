name: Deploy to Amazon ECS

on:
  push:
    branches: ["master"] # Trigger the workflow on push to main branch

env:
  AWS_REGION: us-east-1
  ECR_REPOSITORY: cron-pixelpark-master
  ECS_SERVICE: cron-pixelpark-master-svc
  ECS_CLUSTER: pixelpark-production
  ECS_TASK_DEFINITION: cron-pixelpark-master
  CONTAINER_NAME: cron-pixelpark-master

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    environment: master

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Configure AWS creds
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR master
        if: github.ref == 'refs/heads/master'
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Download task definition master
        if: github.ref == 'refs/heads/master'
        run: |
          aws ecs describe-task-definition --task-definition ${{ env.ECS_TASK_DEFINITION }} --query taskDefinition > task-definition.json

    
      - name: Fill in the new image ID in the Amazon ECS task definition master
        if: github.ref == 'refs/heads/master'
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ steps.build-image.outputs.image }}


      - name: Deploy Amazon ECS task definition master
        if: github.ref == 'refs/heads/master'
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ env.ECS_SERVICE }}
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true