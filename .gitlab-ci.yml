 stages:
  - sonarqube-check
 sonarqube-check:
   stage: sonarqube-check
   tags:
     - self-hosted
   image:
     name: sonarsource/sonar-scanner-cli:latest
     entrypoint: [""]

   variables:
     SONAR_USER_HOME: "./.sonar"  # Defines the location of the analysis task cache
     GIT_DEPTH: "0"
     SONAR_TOKEN: $SONAR_TOKEN
     SONAR_HOST_URL: $SONAR_HOST_URL  # Tells git to fetch all the branches of the project, required by the analysis task
   cache:
     key: "${CI_JOB_NAME}"
     paths:
       - .sonar/cache
   script: 
     - docker run -e SONAR_HOST_URL="${SONAR_HOST_URL}" -e SONAR_LOGIN="${SONAR_TOKEN}" -v ${PWD}:/usr/src  sonarsource/sonar-scanner-cli
   allow_failure: false
   only:
     - dev
