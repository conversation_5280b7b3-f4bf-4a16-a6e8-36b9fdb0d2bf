import Web3 from "web3";
import { ethers } from "ethers";
import { ConfigService } from "@nestjs/config";
import { Injectable } from "@nestjs/common";
@Injectable()
export class Web3Instance {
  private RPC: any;
  constructor(private configService: ConfigService) {
    this.RPC = this.configService.get("MAINNET_RPC");
  }

  getWeb3Instance(): Web3 {
    return new Web3(this.RPC);
  }

  getEtherInstance(): ethers.JsonRpcProvider {
    return new ethers.JsonRpcProvider(this.RPC);
  }

  getRPC(): string {
    return this.RPC;
  }
}
