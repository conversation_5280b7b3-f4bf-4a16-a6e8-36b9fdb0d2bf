export const pixelParkExchangeAbi: any = [
  {
    inputs: [
      {
        components: [
          { internalType: "bool", name: "isOrder<PERSON><PERSON>", type: "bool" },
          { internalType: "address", name: "signer", type: "address" },
          { internalType: "address", name: "collection", type: "address" },
          { internalType: "uint256", name: "price", type: "uint256" },
          { internalType: "uint256", name: "tokenId", type: "uint256" },
          { internalType: "uint256", name: "amount", type: "uint256" },
          { internalType: "address", name: "strategy", type: "address" },
          { internalType: "address", name: "currency", type: "address" },
          { internalType: "uint256", name: "nonce", type: "uint256" },
          { internalType: "uint256", name: "startTime", type: "uint256" },
          { internalType: "uint256", name: "endTime", type: "uint256" },
          {
            internalType: "uint256",
            name: "minPercentageToAsk",
            type: "uint256",
          },
          { internalType: "bytes", name: "params", type: "bytes" },
          { internalType: "uint8", name: "v", type: "uint8" },
          { internalType: "bytes32", name: "r", type: "bytes32" },
          { internalType: "bytes32", name: "s", type: "bytes32" },
        ],
        internalType: "struct OrderTypes.MakerOrder",
        name: "makerAsk",
        type: "tuple",
      },
      {
        components: [
          { internalType: "bool", name: "isOrderAsk", type: "bool" },
          { internalType: "address", name: "signer", type: "address" },
          { internalType: "address", name: "collection", type: "address" },
          { internalType: "uint256", name: "price", type: "uint256" },
          { internalType: "uint256", name: "tokenId", type: "uint256" },
          { internalType: "uint256", name: "amount", type: "uint256" },
          { internalType: "address", name: "strategy", type: "address" },
          { internalType: "address", name: "currency", type: "address" },
          { internalType: "uint256", name: "nonce", type: "uint256" },
          { internalType: "uint256", name: "startTime", type: "uint256" },
          { internalType: "uint256", name: "endTime", type: "uint256" },
          {
            internalType: "uint256",
            name: "minPercentageToAsk",
            type: "uint256",
          },
          { internalType: "bytes", name: "params", type: "bytes" },
          { internalType: "uint8", name: "v", type: "uint8" },
          { internalType: "bytes32", name: "r", type: "bytes32" },
          { internalType: "bytes32", name: "s", type: "bytes32" },
        ],
        internalType: "struct OrderTypes.MakerOrder",
        name: "makerBid",
        type: "tuple",
      },
    ],
    name: "matchMakerBidWithMakerAsk",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
];
