export const GuageEntryAbi: any = [
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    name: "collectionInfo",
    outputs: [
      {
        internalType: "address",
        name: "bribe",
        type: "address",
      },
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
      {
        internalType: "bool",
        name: "whitelistStatus",
        type: "bool",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
];
