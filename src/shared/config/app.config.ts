import { SecretsManager } from "aws-sdk";

/**
 * It fetches the secrets from AWS Secrets Manager and store the secrets as environment variables.
 */
export default async () => {
  try {
    const client = new SecretsManager({
      region: process.env.AWS_REGION,
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
    });

    await client
      .getSecretValue({ SecretId: process.env.AWS_SECRET_NAME })
      .promise()
      .then(secrets => {
        const parsedSecrets = JSON.parse(secrets.SecretString);
        Object.keys(parsedSecrets).forEach(function (key) {
          // Only set the environment variable if it's not already set locally
          if (process.env[key] === undefined) {
            process.env[key] = parsedSecrets[key];
          }
        });
      });
  } catch (error) {
    throw error;
  }
};
