import { Inject } from "@nestjs/common";
import { WINSTON_MODULE_PROVIDER } from "nest-winston";
import { Logger } from "winston";

export class LoggerService {
  constructor(@Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger) {}

  private log(level: "info" | "error", message: string, data?: any) {
    const logMessage = data !== undefined ? `${message}, ${JSON.stringify(data)}` : message;
    this.logger[level](logMessage);
  }

  info(message: string, data?: any) {
    // this.logger.info(`${message}, ${JSON.stringify(data)}`);
    this.log("info", message, data);
  }

  error(message: string, error?: any) {
    // this.logger.error(`${message}, ${JSON.stringify(error)}`);
    this.log("error", message, error);
  }
}
