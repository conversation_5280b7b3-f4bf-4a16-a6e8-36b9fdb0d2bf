import { Collections, CollectionsDocument } from "../../database/schemas/collections.schema";
import { Tokens, TokensDocument } from "../../database/schemas/tokens.schema";
import { Inject, Injectable, InternalServerErrorException, Logger } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { WINSTON_MODULE_PROVIDER } from "nest-winston";

@Injectable()
export class StatisticsUpdateService {
  constructor(
    @InjectModel(Collections.name)
    private collectionModel: Model<CollectionsDocument>,
    @InjectModel(Tokens.name)
    private tokenModel: Model<TokensDocument>,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}
  /**
   * @descriptin Update the floor price for a collection,
   * @param collectionAddress string{ collection address }
   */
  async _updateFloorPrice(collectionAddress: string) {
    try {
      const lowest = await this.tokenModel
        .findOne({
          collectionAddress: collectionAddress,
          buyNow: true,
          price: { $gt: 0 },
        })
        .sort({ price: 1 });
      let updatedFloorPrice = 0;
      if (lowest) {
        updatedFloorPrice = lowest.price;
      }
      await this.collectionModel.findOneAndUpdate(
        { collectionAddress: collectionAddress },
        {
          $set: {
            "stats.floorPrice": updatedFloorPrice,
          },
        },
        {},
      );
      void this._updatePropertyStats(collectionAddress);
    } catch (error) {
      this.logger.error(error?.message, error);
      if (error?.response?.statusCode !== 500) throw error;
      throw new InternalServerErrorException();
    }
  }

  /**
   * @description Recalculates the property stats
   * @param collectionAddress string
   */
  async _updatePropertyStats(collectionAddress: string) {
    try {
      const allToken: any = await this.tokenModel.find({
        collectionAddress: collectionAddress,
      }); //getting all the token from a collection
      const prop = {};
      const allTokenLength = allToken.length;
      allToken.forEach(token => {
        token.properties.forEach(property => {
          //creating a map which consist of percentage count and floorvalue of each property and value
          if (prop[property.traitType]) {
            //if the traitType already exist as key in the map
            if (prop[property.traitType][property.value]) {
              //if the value already exist as key in the traitType map
              prop[property.traitType][property.value].count = prop[property.traitType][property.value].count + 1;
              prop[property.traitType][property.value].percentage = Number(
                ((prop[property.traitType][property.value].count / allTokenLength) * 100).toFixed(2), //percentage of the token that has this property
              );
            } else {
              //if the value doesn't exist as key in the traitType map
              prop[property.traitType][property.value] = {
                count: 1,
                floorPrice: token.price,
                percentage: Number(((1 / allTokenLength) * 100).toFixed(2)),
              };
            }
            if (
              (prop[property.traitType][property.value].floorPrice && prop[property.traitType][property.value].floorPrice > token.price && token.price > 0) ||
              !prop[property.traitType][property.value].floorPrice
            ) {
              //checking if the price of the token is less then we are replacing floor price
              prop[property.traitType][property.value].floorPrice = token.price;
            }
          } else {
            //if there is no traitType in the map then we create the traitType key which has value as inner key
            prop[property.traitType] = {
              [property.value]: {
                count: 1,
                floorPrice: token.price,
                percentage: Number(((1 / allTokenLength) * 100).toFixed(2)),
              },
            };
          }
        });
      });
      await this.saveProperties(allToken, prop);
    } catch (error) {
      this.logger.error(`updatePropertyStats ${error?.message}`);
    }
  }

  /**
   * @description Updates the property of all token in particular collection
   * @param allToken Token[]
   * @param prop Object
   */
  async saveProperties(allToken: any, prop: any) {
    try {
      const savePromise = [];
      allToken.forEach(token => {
        token.properties.forEach(property => {
          property.count = prop[property.traitType][property.value].count;
          property.floorPrice = prop[property.traitType][property.value].floorPrice;
          property.percentage = prop[property.traitType][property.value].percentage;
        });
        savePromise.push(this.tokenModel.findByIdAndUpdate(token._id, { properties: token.properties }, {}));
      });
      await Promise.all(savePromise);
    } catch (error) {
      this.logger.error(`saveProperties ${error?.message}`);
    }
  }
}
