import { Injectable, Logger } from "@nestjs/common";
import * as os from "os";
// import * as fs from "fs";
// import * as path from "path";
import { exec } from "child_process";
import { promisify } from "util";

export interface ResourceMetrics {
  timestamp: Date;
  process: {
    pid: number;
    uptime: number;
    memory: {
      rss: number;
      heapTotal: number;
      heapUsed: number;
      external: number;
      arrayBuffers: number;
    };
    cpu: {
      user: number;
      system: number;
    };
  };
  system: {
    memory: {
      total: number;
      free: number;
      used: number;
      usedPercent: number;
    };
    cpu: {
      loadAverage: number[];
      cores: number;
    };
  };
  network: {
    connections: number;
    ports: number;
  };
  threads: {
    count: number;
  };
}

@Injectable()
export class ResourceMonitorService {
  private readonly logger = new Logger(ResourceMonitorService.name);
  private monitoringInterval: NodeJS.Timeout | null = null;
  private lastCpuUsage = process.cpuUsage();
  private metricsHistory: ResourceMetrics[] = [];
  private readonly maxHistorySize = 100; // Keep last 100 measurements

  // Thresholds for alerts
  private readonly thresholds = {
    memory: {
      warning: 1024 * 1024 * 1024, // 1GB
      critical: 2048 * 1024 * 1024, // 2GB
    },
    systemMemory: {
      warning: 80, // 80%
      critical: 90, // 90%
    },
    cpu: {
      warning: 70, // 70%
      critical: 90, // 90%
    },
    connections: {
      warning: 200,
      critical: 300,
    },
    threads: {
      warning: 50,
      critical: 100,
    },
  };

  startMonitoring(intervalMs = 30000): void {
    if (this.monitoringInterval) {
      this.logger.warn("Resource monitoring is already running");
      return;
    }

    this.logger.log(`Starting resource monitoring with ${intervalMs}ms interval`);

    this.monitoringInterval = setInterval(() => {
      void this.collectMetrics();
    }, intervalMs);

    // Initial collection
    void this.collectMetrics();
  }

  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      this.logger.log("Resource monitoring stopped");
    }
  }

  private async collectMetrics(): Promise<void> {
    try {
      const metrics = await this.gatherMetrics();
      this.addToHistory(metrics);
      this.checkThresholds(metrics);
      this.logMetrics(metrics);
    } catch (error) {
      this.logger.error("Error collecting metrics:", error);
    }
  }

  private async gatherMetrics(): Promise<ResourceMetrics> {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage(this.lastCpuUsage);
    this.lastCpuUsage = process.cpuUsage();

    const systemMemory = {
      total: os.totalmem(),
      free: os.freemem(),
      used: os.totalmem() - os.freemem(),
      usedPercent: Math.round(((os.totalmem() - os.freemem()) / os.totalmem()) * 100),
    };

    const networkStats = await this.getNetworkStats();
    const threadCount = await this.getThreadCount();

    return {
      timestamp: new Date(),
      process: {
        pid: process.pid,
        uptime: process.uptime(),
        memory: {
          rss: memUsage.rss,
          heapTotal: memUsage.heapTotal,
          heapUsed: memUsage.heapUsed,
          external: memUsage.external,
          arrayBuffers: memUsage.arrayBuffers,
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system,
        },
      },
      system: {
        memory: systemMemory,
        cpu: {
          loadAverage: os.loadavg(),
          cores: os.cpus().length,
        },
      },
      network: networkStats,
      threads: {
        count: threadCount,
      },
    };
  }

  private async getNetworkStats(): Promise<{ connections: number; ports: number }> {
    try {
      // On macOS/Linux, use netstat to count connections
      // const { exec } = require("child_process");
      // const { promisify } = require("util");
      const execAsync = promisify(exec);

      const { stdout } = await execAsync(`netstat -an | grep ${process.pid} | wc -l`);
      const connections = parseInt(stdout.trim()) || 0;

      // Count open file descriptors (approximation for ports)
      let ports = 0;
      try {
        if (process.platform === "darwin" || process.platform === "linux") {
          const { stdout: lsofOutput } = await execAsync(`lsof -p ${process.pid} | grep -c TCP`);
          ports = parseInt(lsofOutput.trim()) || 0;
        }
      } catch (error) {
        // Fallback if lsof is not available
        ports = 0;
      }

      return { connections, ports };
    } catch (error) {
      return { connections: 0, ports: 0 };
    }
  }

  private async getThreadCount(): Promise<number> {
    try {
      if (process.platform === "darwin") {
        // const { exec } = require("child_process");
        // const { promisify } = require("util");
        const execAsync = promisify(exec);

        const { stdout } = await execAsync(`ps -M ${process.pid} | wc -l`);
        return Math.max(0, parseInt(stdout.trim()) - 1); // Subtract header line
      } else if (process.platform === "linux") {
        // const { exec } = require("child_process");
        // const { promisify } = require("util");
        const execAsync = promisify(exec);

        const { stdout } = await execAsync(`ps -o nlwp= -p ${process.pid}`);
        return parseInt(stdout.trim()) || 0;
      }
    } catch (error) {
      // Fallback: estimate based on worker threads if available
      return 1;
    }
    return 1;
  }

  private addToHistory(metrics: ResourceMetrics): void {
    this.metricsHistory.push(metrics);
    if (this.metricsHistory.length > this.maxHistorySize) {
      this.metricsHistory.shift();
    }
  }

  private checkThresholds(metrics: ResourceMetrics): void {
    const memoryMB = Math.round(metrics.process.memory.rss / 1024 / 1024);

    // Memory checks
    if (metrics.process.memory.rss > this.thresholds.memory.critical) {
      this.logger.error(`🚨 CRITICAL: Process memory usage: ${memoryMB}MB`);
    } else if (metrics.process.memory.rss > this.thresholds.memory.warning) {
      this.logger.warn(`⚠️ WARNING: High process memory usage: ${memoryMB}MB`);
    }

    // System memory checks
    if (metrics.system.memory.usedPercent > this.thresholds.systemMemory.critical) {
      this.logger.error(`🚨 CRITICAL: System memory usage: ${metrics.system.memory.usedPercent}%`);
    } else if (metrics.system.memory.usedPercent > this.thresholds.systemMemory.warning) {
      this.logger.warn(`⚠️ WARNING: High system memory usage: ${metrics.system.memory.usedPercent}%`);
    }

    // Thread count checks
    if (metrics.threads.count > this.thresholds.threads.critical) {
      this.logger.error(`🚨 CRITICAL: Thread count: ${metrics.threads.count}`);
    } else if (metrics.threads.count > this.thresholds.threads.warning) {
      this.logger.warn(`⚠️ WARNING: High thread count: ${metrics.threads.count}`);
    }

    // Network connection checks
    if (metrics.network.connections > this.thresholds.connections.critical) {
      this.logger.error(`🚨 CRITICAL: Network connections: ${metrics.network.connections}`);
    } else if (metrics.network.connections > this.thresholds.connections.warning) {
      this.logger.warn(`⚠️ WARNING: High network connections: ${metrics.network.connections}`);
    }
  }

  private logMetrics(metrics: ResourceMetrics): void {
    const memoryMB = Math.round(metrics.process.memory.rss / 1024 / 1024);
    const heapUsedMB = Math.round(metrics.process.memory.heapUsed / 1024 / 1024);
    const heapTotalMB = Math.round(metrics.process.memory.heapTotal / 1024 / 1024);

    this.logger.log(
      `📊 Resources: Memory ${memoryMB}MB (Heap: ${heapUsedMB}/${heapTotalMB}MB), ` +
        `System: ${metrics.system.memory.usedPercent}%, ` +
        `Threads: ${metrics.threads.count}, ` +
        `Connections: ${metrics.network.connections}, ` +
        `Ports: ${metrics.network.ports}, ` +
        `Uptime: ${Math.round(metrics.process.uptime)}s`,
    );
  }

  getLatestMetrics(): ResourceMetrics | null {
    return this.metricsHistory.length > 0 ? this.metricsHistory[this.metricsHistory.length - 1] : null;
  }

  getMetricsHistory(): ResourceMetrics[] {
    return [...this.metricsHistory];
  }

  getAverageMetrics(lastN = 10): Partial<ResourceMetrics> | null {
    if (this.metricsHistory.length === 0) return null;

    const recentMetrics = this.metricsHistory.slice(-lastN);
    const count = recentMetrics.length;

    const avgMemory = recentMetrics.reduce((sum, m) => sum + m.process.memory.rss, 0) / count;
    const avgSystemMemory = recentMetrics.reduce((sum, m) => sum + m.system.memory.usedPercent, 0) / count;
    const avgThreads = recentMetrics.reduce((sum, m) => sum + m.threads.count, 0) / count;
    const avgConnections = recentMetrics.reduce((sum, m) => sum + m.network.connections, 0) / count;

    return {
      process: {
        memory: {
          rss: Math.round(avgMemory),
        },
      },
      system: {
        memory: {
          usedPercent: Math.round(avgSystemMemory),
        },
      },
      threads: {
        count: Math.round(avgThreads),
      },
      network: {
        connections: Math.round(avgConnections),
      },
    } as Partial<ResourceMetrics>;
  }

  exportMetrics(): string {
    return JSON.stringify(this.metricsHistory, null, 2);
  }
}
