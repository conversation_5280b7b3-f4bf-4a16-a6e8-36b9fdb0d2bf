import { Injectable, Logger } from '@nestjs/common';
import { Worker } from 'worker_threads';
import * as os from 'os';

interface WorkerInfo {
  worker: Worker;
  startTime: Date;
  blockStart: number;
  workerId: number;
  retryCount: number;
  maxRetries: number;
}

@Injectable()
export class ProcessMonitorService {
  private readonly logger = new Logger(ProcessMonitorService.name);
  private activeWorkers = new Map<number, WorkerInfo>();
  private workerTimeouts = new Map<number, NodeJS.Timeout>();
  private isShuttingDown = false;

  constructor() {
    // Handle process signals for graceful shutdown
    process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => this.gracefulShutdown('SIGINT'));
    process.on('uncaughtException', (error) => {
      this.logger.error('Uncaught Exception:', error);
      this.gracefulShutdown('uncaughtException');
    });
    process.on('unhandledRejection', (reason, promise) => {
      this.logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    });
  }

  /**
   * Create a monitored worker with automatic retry and cleanup
   */
  createMonitoredWorker(
    workerScript: string,
    workerData: any,
    workerId: number,
    maxRetries: number = 3,
    timeoutMs: number = 180000 // 3 minutes
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      if (this.isShuttingDown) {
        reject(new Error('Service is shutting down'));
        return;
      }

      this.startWorker(workerScript, workerData, workerId, maxRetries, timeoutMs, resolve, reject);
    });
  }

  private startWorker(
    workerScript: string,
    workerData: any,
    workerId: number,
    maxRetries: number,
    timeoutMs: number,
    resolve: (value: any) => void,
    reject: (reason: any) => void,
    retryCount: number = 0
  ) {
    // Check system resources before starting worker
    if (!this.checkSystemResources()) {
      reject(new Error('Insufficient system resources to start worker'));
      return;
    }

    const worker = new Worker(workerScript, {
      workerData,
      resourceLimits: {
        maxOldGenerationSizeMb: 512,
        maxYoungGenerationSizeMb: 128,
      },
    });

    const workerInfo: WorkerInfo = {
      worker,
      startTime: new Date(),
      blockStart: workerData.blockStart,
      workerId,
      retryCount,
      maxRetries,
    };

    this.activeWorkers.set(workerId, workerInfo);

    // Set timeout for worker
    const timeout = setTimeout(() => {
      this.logger.warn(`Worker ${workerId} timed out after ${timeoutMs}ms`);
      this.cleanupWorker(workerId);
      
      if (retryCount < maxRetries) {
        this.logger.log(`Retrying worker ${workerId} (attempt ${retryCount + 1}/${maxRetries})`);
        setTimeout(() => {
          this.startWorker(workerScript, workerData, workerId, maxRetries, timeoutMs, resolve, reject, retryCount + 1);
        }, 1000 * Math.pow(2, retryCount)); // Exponential backoff
      } else {
        reject(new Error(`Worker ${workerId} failed after ${maxRetries} retries`));
      }
    }, timeoutMs);

    this.workerTimeouts.set(workerId, timeout);

    worker.on('message', (data) => {
      this.logger.debug(`Worker ${workerId} completed successfully`);
      this.cleanupWorker(workerId);
      resolve(data);
    });

    worker.on('error', (error) => {
      this.logger.error(`Worker ${workerId} error: ${error.message}`);
      this.cleanupWorker(workerId);

      if (retryCount < maxRetries) {
        this.logger.log(`Retrying worker ${workerId} due to error (attempt ${retryCount + 1}/${maxRetries})`);
        setTimeout(() => {
          this.startWorker(workerScript, workerData, workerId, maxRetries, timeoutMs, resolve, reject, retryCount + 1);
        }, 1000 * Math.pow(2, retryCount));
      } else {
        reject(error);
      }
    });

    worker.on('exit', (code) => {
      this.logger.debug(`Worker ${workerId} exited with code ${code}`);
      this.cleanupWorker(workerId);

      if (code !== 0 && retryCount < maxRetries) {
        this.logger.log(`Retrying worker ${workerId} due to exit code ${code} (attempt ${retryCount + 1}/${maxRetries})`);
        setTimeout(() => {
          this.startWorker(workerScript, workerData, workerId, maxRetries, timeoutMs, resolve, reject, retryCount + 1);
        }, 1000 * Math.pow(2, retryCount));
      } else if (code !== 0) {
        reject(new Error(`Worker ${workerId} exited with code ${code} after ${maxRetries} retries`));
      }
    });

    this.logger.debug(`Started worker ${workerId} (attempt ${retryCount + 1}/${maxRetries + 1})`);
  }

  private cleanupWorker(workerId: number): void {
    const workerInfo = this.activeWorkers.get(workerId);
    if (workerInfo) {
      try {
        workerInfo.worker.terminate();
      } catch (error) {
        this.logger.error(`Error terminating worker ${workerId}:`, error);
      }
      this.activeWorkers.delete(workerId);
    }

    const timeout = this.workerTimeouts.get(workerId);
    if (timeout) {
      clearTimeout(timeout);
      this.workerTimeouts.delete(workerId);
    }
  }

  private checkSystemResources(): boolean {
    const memUsage = process.memoryUsage();
    const systemMemory = {
      total: os.totalmem(),
      free: os.freemem(),
      usedPercent: Math.round(((os.totalmem() - os.freemem()) / os.totalmem()) * 100),
    };

    // Check if system memory is critically low
    if (systemMemory.usedPercent > 95) {
      this.logger.error(`🚨 CRITICAL SYSTEM MEMORY: ${systemMemory.usedPercent}% used`);
      return false;
    }

    // Check if process memory is too high
    const processMemoryMB = Math.round(memUsage.rss / 1024 / 1024);
    if (processMemoryMB > 2048) { // 2GB limit
      this.logger.error(`🚨 CRITICAL PROCESS MEMORY: ${processMemoryMB}MB used`);
      return false;
    }

    return true;
  }

  getActiveWorkerCount(): number {
    return this.activeWorkers.size;
  }

  getWorkerInfo(): WorkerInfo[] {
    return Array.from(this.activeWorkers.values());
  }

  async gracefulShutdown(signal?: string): Promise<void> {
    if (this.isShuttingDown) return;
    this.isShuttingDown = true;

    this.logger.info(`Graceful shutdown initiated${signal ? ` (${signal})` : ''}`);

    // Terminate all active workers
    const workers = Array.from(this.activeWorkers.keys());
    for (const workerId of workers) {
      this.cleanupWorker(workerId);
    }

    // Clear all timeouts
    for (const timeout of this.workerTimeouts.values()) {
      clearTimeout(timeout);
    }
    this.workerTimeouts.clear();

    this.logger.info('All workers terminated, shutdown complete');
  }
}
