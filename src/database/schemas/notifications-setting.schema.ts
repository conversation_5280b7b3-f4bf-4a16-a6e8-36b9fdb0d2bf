import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";

export type NotificationSettingDocument = NotificationSetting & Document;

@Schema({ timestamps: true })
export class NotificationSetting {
  @Prop()
  address: string;

  @Prop({ default: true })
  itemSold: boolean;

  @Prop({ default: true })
  ownedItem: boolean;

  @Prop({ default: true })
  bidActivity: boolean;

  @Prop({ default: true })
  priceChange: boolean;

  @Prop({ default: true })
  auctionEnd: boolean;

  @Prop({ default: true })
  outBid: boolean;

  @Prop({ default: true })
  purchaseSuccess: boolean;

  @Prop({ default: true })
  newsLetter: boolean;

  @Prop({ default: 0 })
  thresholdBid: number;
}
const NotificationSettingSchema = SchemaFactory.createForClass(NotificationSetting);

NotificationSettingSchema.index({ address: -1 }, { unique: true });

export { NotificationSettingSchema };
