import { CommonStatus } from "../enums/common-status.enum";
import { Roles } from "../enums/roles.enum";
import { constants } from "../../shared/constants/constant";

import { Document, Schema as MongooseSchema } from "mongoose";
import { <PERSON><PERSON>, Schema, SchemaFactory } from "@nestjs/mongoose";

export type UsersDocument = Users & Document;

export class Avatar {
  @Prop()
  logo: string;

  @Prop()
  contentType: string;
}

export class ArtistStats {
  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  pixelEarned: number;

  @Prop({ default: 0 })
  currentVotes: number;

  @Prop({ default: 0 })
  lastVotes: number;

  @Prop({ default: 0 })
  voteChange: number;

  @Prop({ type: MongooseSchema.Types.Decimal128, default: 0 })
  totalTradingVolume: number;

  @Prop({ type: MongooseSchema.Types.Decimal128, default: 0 })
  currentTradingVolume: number;

  @Prop({ default: 0 })
  totalVolumeChange: number;

  @Prop({ default: 0 })
  currentVolumeChange: number;
}

export class BribeTokens {
  @Prop({ default: null })
  bribeTokenAddress: string;

  @Prop({ default: null })
  decimals: string;

  @Prop({ default: null })
  symbol: string;

  @Prop({ default: null })
  name: string;
}

export class Bribes {
  @Prop({ type: MongooseSchema.Types.Decimal128 })
  rewardAmount: number;

  @Prop({ type: MongooseSchema.Types.Decimal128 })
  rewardRate: number;

  @Prop({ default: {} })
  bribeTokens: BribeTokens;
}

export class Cover {
  @Prop()
  logo: string;

  @Prop()
  contentType: string;
}

@Schema({ timestamps: true })
export class Users {
  @Prop()
  address: string;

  @Prop()
  name: string;

  @Prop()
  email: string;

  @Prop()
  avatar: Avatar;

  @Prop()
  cover: Cover;

  @Prop()
  biography: string;

  @Prop({ default: 0 })
  countCollections: number;

  @Prop()
  instagramLink: string;

  @Prop({ default: 1 })
  nonce: number;

  @Prop()
  twitterLink: string;

  @Prop()
  websiteLink: string;

  @Prop()
  youtubeLink: string;

  @Prop()
  telegramLink: string;

  @Prop({ defaultValue: null })
  phishingPhrase: string;

  @Prop()
  discordLink: string;

  @Prop({ default: false })
  isVerified: boolean;

  @Prop({ type: MongooseSchema.Types.Decimal128 })
  walletBalance: number;

  @Prop({ default: null })
  elasticsearchId: string;

  @Prop({ default: true })
  elasticsearchUpdate: boolean;

  @Prop({ default: Roles.USER })
  roles: Roles;

  @Prop({
    default: CommonStatus.ACTIVE,
    description: "Indicates if the account is deleted, active or blocked",
  })
  status: CommonStatus;

  @Prop({
    default: CommonStatus.INACTIVE,
    description: "Indicates if the user has opted to share nfts/collection to stores",
  })
  affiliateStatus: CommonStatus;

  @Prop({ default: Date.now() })
  lastLoggedIn: Date;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: Users.name,
    default: null,
    description: "Indicates which admon blocked the user",
  })
  blockedBy: Users;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: Users.name,
    default: null,
    description: "Indicates which super admin added the user",
  })
  adminAddedBy: Users;

  @Prop({
    default: null,
    description: "Indicates date at which user was blocked",
  })
  blockedDate: Date;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: constants.storeFront,
    default: null,
  })
  storeId: string;

  @Prop({
    default: CommonStatus.INACTIVE,
    description: "Indicates if the user is artist or not",
  })
  artistStatus: CommonStatus;

  @Prop({ default: false })
  marketingEnabled: boolean;

  @Prop({ default: 0 })
  commissionRate: number;

  @Prop({ default: false })
  isVotingEnabled: boolean;

  @Prop({ default: null })
  bribeAddress: string;

  @Prop({ type: Array, default: [] })
  bribes: Bribes[];

  @Prop({ default: {} })
  artistStats: ArtistStats;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "VoteCycle",
    default: null,
  })
  verifyCycleId: string;

  @Prop({ default: false })
  isVerifyEnabled: boolean;
}

const UsersSchema = SchemaFactory.createForClass(Users);

UsersSchema.index({ address: -1 }, { unique: true });
UsersSchema.index({ elasticsearchId: 1 });
UsersSchema.index({ elasticsearchUpdate: 1 });

export { UsersSchema };
