import { constants } from "../../shared/constants/constant";
import { <PERSON>p, <PERSON>hema, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

export type OffersDocument = Offers & Document;

@Schema({ timestamps: true })
export class Offers {
  @Prop()
  isOrderAsk: boolean;

  @Prop()
  signer: string;

  @Prop()
  collectionAddress: string;

  @Prop({ type: MongooseSchema.Types.Decimal128 })
  price: number;

  @Prop()
  tokenId: string;

  @Prop()
  amount: number;

  @Prop()
  strategy: string;

  @Prop()
  currency: string;

  @Prop()
  nonce: number;

  @Prop()
  startTime: number;

  @Prop()
  endTime: number;

  @Prop()
  minPercentageToAsk: number;

  @Prop({ default: [] })
  params: string[];

  @Prop()
  signature: string;

  @Prop({ default: true })
  active: boolean;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: constants.auction,
    default: null,
    strictPopulale: false,
  })
  auctionId: string;

  @Prop()
  hash: string;
}

const OffersSchema = SchemaFactory.createForClass(Offers);

OffersSchema.index({ tokenId: -1, collectionAddress: -1 });

OffersSchema.virtual("collectionInfo", {
  type: MongooseSchema.Types.String,
  ref: "Collections",
  localField: "collectionAddress",
  foreignField: "collectionAddress",
  justOne: true,
});

OffersSchema.virtual("tokenInfo", {
  type: MongooseSchema.Types.String,
  ref: "Tokens",
  localField: "tokenId",
  foreignField: "tokenId",
  justOne: true,
});

OffersSchema.virtual("userInfo", {
  type: MongooseSchema.Types.String,
  ref: "Users",
  localField: "signer",
  foreignField: "address",
  justOne: true,
});

OffersSchema.virtual("auctionInfo", {
  type: MongooseSchema.Types.ObjectId,
  ref: "Auction",
  localField: "auctionId",
  foreignField: "_id",
  justOne: true,
});

export { OffersSchema };
