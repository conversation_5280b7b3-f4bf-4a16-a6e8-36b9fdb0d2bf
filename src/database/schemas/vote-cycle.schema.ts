import { VotingType } from "../enums/voting-type.enum";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

export type VoteCycleDocument = VoteCycle & Document;

@Schema({ timestamps: true })
export class VoteCycle {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: "Users" })
  artistId: MongooseSchema.Types.ObjectId;

  @Prop()
  startDate: number;

  @Prop()
  endDate: number;

  @Prop({ default: true })
  active: boolean;

  @Prop({ default: false })
  isClaimed: boolean;

  @Prop({ default: VotingType.VERIFICATION })
  type: VotingType;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  claimedAmount: string;
}
export const VoteCycleSchema = SchemaFactory.createForClass(VoteCycle);
