import { Status } from "../../shared/enums/status.enum";

import { constants } from "../../shared/constants/constant";
import { ChainType } from "../../shared/enums/chain-type.enum";
import { TokenStandard } from "../../shared/enums/token-standard.enum";
import { Document, Schema as MongooseSchema } from "mongoose";
import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";

export type CollectionsDocument = Collections & Document;

export class CollectionLogo {
  @Prop({ default: null })
  url: string;

  @Prop({ default: null })
  contentType: string;
}

export class CollectionStats {
  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  floorPrice?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  floorPriceOneDay?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  floorPriceOneDayChange?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  oneDaySale?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  oneDayChange?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  oneDayVolume?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  oneDayAvgPrice?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  sevenDaySale?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  sevenDayChange?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  sevenDayVolume?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  sevenDayAvgPrice?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  thirtyDaySale?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  thirtyDayChange?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  thirtyDayVolume?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  thirtyDayAvgPrice?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  totalVolume?: number;
}

@Schema({ timestamps: true })
export class Collections {
  @Prop()
  collectionAddress: string;

  @Prop()
  owner: string;

  @Prop({
    type: MongooseSchema.Types.Decimal128,
  })
  blockNo: number;

  @Prop()
  name: string;

  @Prop()
  slug: string;

  @Prop({ default: null })
  description: string;

  @Prop()
  tokenStandard: TokenStandard;

  @Prop()
  logo: CollectionLogo;

  @Prop({ default: null })
  banner: CollectionLogo;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: constants.category,
    default: null,
  })
  categoryId: MongooseSchema.Types.ObjectId;

  @Prop({ default: null })
  projectSaleStartDate: Date;

  @Prop({ default: null })
  projectSaleEndDate: Date;

  @Prop({ default: null })
  externalUrl: string;

  @Prop({ default: null })
  twitterLink: string;

  @Prop({ default: null })
  discordLink: string;

  @Prop({ default: null })
  telegramLink: string;

  @Prop({ default: null })
  mediumLink: string;

  @Prop({ default: null })
  instagramLink: string;

  @Prop({ default: null })
  royaltyRate: number;

  @Prop({ default: false })
  isExplicit: boolean;

  @Prop({ default: null })
  royaltyWallet: string;

  @Prop({ default: false })
  isHidden: boolean;

  @Prop({ default: false })
  isVerified: boolean;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  totalSupply: number;

  @Prop({ default: {} })
  stats: CollectionStats;

  @Prop({ default: false })
  isVerifyEnabled: boolean;

  @Prop({ default: [] })
  likes: [string];

  @Prop({ default: ChainType.PULSE })
  blockChain: ChainType;

  @Prop({ default: Status.ACTIVE })
  status: Status;

  @Prop({ default: null })
  elasticsearchId: string;

  @Prop({ default: true })
  elasticsearchUpdate: boolean;

  @Prop({ default: false })
  isUserBlacklisted: boolean;

  @Prop({ default: null })
  collectionCreatedAt: Date;

  @Prop({ default: null })
  collectionUpdatedAt: Date;
}

const CollectionsSchema = SchemaFactory.createForClass(Collections);

CollectionsSchema.index({ name: "text" });
CollectionsSchema.index({ collectionAddress: -1 }, { unique: true });
CollectionsSchema.index({ elasticsearchId: 1 });
CollectionsSchema.index({ elasticsearchUpdate: 1 });

CollectionsSchema.virtual("collectionVotes", {
  type: MongooseSchema.Types.String,
  ref: "VerifyVoting",
  localField: "collectionAddress",
  foreignField: "collectionAddress",
  justOne: true,
});

export { CollectionsSchema };
