import { VoteCycle } from "./vote-cycle.schema";
import { Users } from "./users.schema";
import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

export type GuageVotingDocument = GuageVoting & Document;

@Schema({ timestamps: true })
export class GuageVoting {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: Users.name,
  })
  artistId: MongooseSchema.Types.ObjectId;

  @Prop({ default: false })
  isPositiveVote: boolean;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: Users.name })
  voterId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: VoteCycle.name })
  cycleId: MongooseSchema.Types.ObjectId;

  @Prop()
  veTokenId: string;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  voteAmount: string;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  claimedAmount: string;
}
export const GuageVotingSchema = SchemaFactory.createForClass(GuageVoting);
