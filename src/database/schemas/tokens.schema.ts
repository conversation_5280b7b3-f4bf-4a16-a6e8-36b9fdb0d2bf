import { TokenStandard } from "../../shared/enums/token-standard.enum";
import { CommonStatus } from "../enums/common-status.enum";
import { AuctionType } from "../../shared/enums/auction-type.enum";
import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

export type TokensDocument = Tokens & Document;

export class TokenLogo {
  @Prop({ default: null })
  url: string;

  @Prop({ default: null })
  animationUrl: string;

  @Prop({ default: null })
  contentType: string;
}
export class OwnerIds {
  @Prop({ default: null })
  walletAddress: string;

  @Prop({ default: 1, type: MongooseSchema.Types.Decimal128 })
  qty: number;
}

export class TokenProperties {
  @Prop({ default: null })
  traitType: string;

  @Prop({ default: null })
  value: string;

  @Prop({ default: 0 })
  count: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  floorPrice?: number;

  @Prop({ default: 0 })
  percentage: number;
}

@Schema({ timestamps: true, toJSON: { virtuals: true } })
export class Tokens {
  @Prop()
  tokenId: string;

  @Prop({
    type: MongooseSchema.Types.Decimal128,
  })
  blockNo: number;

  @Prop()
  collectionAddress: string;

  @Prop({ default: null })
  description: string;

  @Prop()
  name: string;

  @Prop()
  logo: TokenLogo;

  @Prop({ default: false })
  isBundle: boolean;

  @Prop({ default: false })
  isExplicit: boolean;

  @Prop({ default: null })
  bundleName: string;

  @Prop({ default: null })
  bundleDescription: string;

  @Prop()
  tokenStandard: TokenStandard;

  @Prop({ default: false })
  isHidden: boolean;

  @Prop({ default: false })
  isRefreshed: boolean;

  @Prop({ default: [] })
  likes: [string];

  @Prop({ type: Array, default: [] })
  properties: TokenProperties[];

  @Prop({ type: Array, default: [] })
  currentOwnerIds: OwnerIds[];

  @Prop({ default: null })
  originalOwnerId: string;

  @Prop({ default: CommonStatus.ACTIVE })
  status: CommonStatus;

  @Prop({
    default: null,
    type: MongooseSchema.Types.Decimal128,
  })
  price: number;

  @Prop({ default: null })
  tokenCreatedAt: Date;

  @Prop({ default: null })
  tokenUpdatedAt: Date;

  @Prop({ default: null })
  listedAt: Date;

  @Prop({ default: null })
  elasticsearchId: string;

  @Prop({ default: true })
  elasticsearchUpdate: boolean;

  @Prop({ default: false })
  isCollectionBlacklisted: boolean;

  @Prop({ default: false })
  inArtistRegistry: boolean;

  @Prop({ default: null })
  royaltyRate: number;

  @Prop({ default: null })
  royaltyWallet: string;

  @Prop({ default: false })
  buyNow: boolean;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "Auction",
    default: null,
  })
  auctionId: MongooseSchema.Types.ObjectId;

  @Prop({ default: null })
  auctionType: AuctionType;

  @Prop({ default: null })
  auctionEndTime: number;

  @Prop({ default: false })
  reserveMet: boolean;

  @Prop({ default: false })
  hasOffer: boolean;

  @Prop({ default: null, type: MongooseSchema.Types.Decimal128 })
  topOffer: number;

  @Prop({ default: null })
  transactionHash: string;

  @Prop({ default: null })
  fromUserId: string;
}

const TokensSchema = SchemaFactory.createForClass(Tokens);

TokensSchema.index({ name: "text" });
TokensSchema.index({ tokenId: -1 });
TokensSchema.index({ tokenId: -1, collectionAddress: -1 }, { unique: true });
TokensSchema.index({ originalOwnerId: -1 });
TokensSchema.index({ elasticsearchId: 1 });
TokensSchema.index({ elasticsearchUpdate: 1 });
TokensSchema.index({ collectionAddress: 1 });
TokensSchema.index({ "currentOwnerIds.walletAddress": 1, isHidden: 1 });
TokensSchema.index({ createdAt: 1 });

TokensSchema.virtual("collectionInfo", {
  type: MongooseSchema.Types.String,
  ref: "Collections",
  localField: "collectionAddress",
  foreignField: "collectionAddress",
  justOne: true,
});

export { TokensSchema };
