import { VoteCycle } from "./vote-cycle.schema";
import { Users } from "./users.schema";
import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

export type ArtistVerificationVotingDocument = ArtistVerificationVoting & Document;

@Schema({ timestamps: true })
export class ArtistVerificationVoting {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: Users.name })
  artistId: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: VoteCycle.name })
  voteCycleId: string;

  @Prop({ default: false })
  voteCasted: boolean;

  @Prop()
  walletAddress: string;

  @Prop({ default: true })
  active: boolean;
}
export const ArtistVerificationVotingSchema = SchemaFactory.createForClass(ArtistVerificationVoting);
