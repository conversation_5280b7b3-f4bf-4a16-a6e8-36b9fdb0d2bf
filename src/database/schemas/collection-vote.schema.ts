import { VoteCycle } from "./vote-cycle.schema";
import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

export type CollectionVotingDocument = CollectionVoting & Document;

@Schema({ timestamps: true })
export class CollectionVoting {
  @Prop({ type: String })
  collectionAddress: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: VoteCycle.name })
  voteCycleId: string;

  @Prop({ default: false })
  voteCasted: boolean;

  @Prop()
  walletAddress: string;

  @Prop({ default: true })
  active: boolean;
}
export const CollectionVotingSchema = SchemaFactory.createForClass(CollectionVoting);
