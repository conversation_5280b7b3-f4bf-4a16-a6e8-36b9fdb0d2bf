import { TokenHistoryType } from "../enums/token-history-type.enum";
import { <PERSON>p, <PERSON>hema, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

export type TokenHistoryDocument = TokenHistory & Document;

@Schema({ timestamps: true })
export class TokenHistory {
  @Prop()
  type: TokenHistoryType;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  amount: number;

  @Prop({ default: null })
  blockNumber: string;

  @Prop({ default: null })
  blockTimestamp: string;
}
export const TokenHistorySchema = SchemaFactory.createForClass(TokenHistory);
