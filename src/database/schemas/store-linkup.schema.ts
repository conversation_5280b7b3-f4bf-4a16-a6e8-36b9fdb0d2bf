import { Collections } from "./collections.schema";
import { Tokens } from "./tokens.schema";
import { StoreFront } from "./store-front.schema";
import { EntityType } from "../enums/entity-type.enum";
import { Document, Schema as MongooseSchema } from "mongoose";
import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";

export type StoreLinkupDocument = StoreLinkup & Document;

@Schema({ timestamps: true })
export class StoreLinkup {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: StoreFront.name,
    default: null,
  })
  storeId: string;

  @Prop()
  entityType: EntityType;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: Collections.name,
    default: null,
  })
  collectionId: string;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: Tokens.name,
    default: null,
  })
  tokenId: string;

  @Prop({ default: false })
  isGalleryItem: boolean;

  @Prop({ default: true })
  isActive: boolean;
}
export const StoreLinkupSchema = SchemaFactory.createForClass(StoreLinkup);
