import { Tokens } from "./tokens.schema";
import { Collections } from "./collections.schema";
import { EntityType } from "../enums/entity-type.enum";
import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

export type BlockDataDocument = BlockData & Document;

@Schema({ timestamps: true })
export class BlockData {
  @Prop()
  type: EntityType;

  @Prop({ default: null })
  insertedAt: Date;

  @Prop({ default: null })
  tokenData: Tokens;

  @Prop({ default: null })
  collectionData: Collections;

  @Prop({
    type: MongooseSchema.Types.Decimal128,
  })
  blockNumber: number;
}

export const BlockDataSchema = SchemaFactory.createForClass(BlockData);

BlockDataSchema.index({ blockNumber: 1, type: 1 });
