import { AuctionType } from "../../shared/enums/auction-type.enum";
import { Prop, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

export type AuctionDocument = Auction & Document;

@Schema({ timestamps: true })
export class Auction {
  @Prop()
  type: AuctionType;

  @Prop()
  tokenId: string;

  @Prop({ default: 1 })
  quantity: number;

  @Prop()
  fromUser: string;

  @Prop({ default: null })
  toUser: string;

  @Prop({ type: MongooseSchema.Types.Decimal128 })
  startingPrice: number;

  @Prop({
    default: null,
    type: MongooseSchema.Types.Decimal128,
  })
  reservedPrice: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  currentPrice: number;

  @Prop({ default: true })
  status: boolean;

  @Prop()
  isOrderAsk: boolean;

  @Prop()
  signer: string;

  @Prop()
  collectionAddress: string;

  @Prop({ type: MongooseSchema.Types.Decimal128 })
  price: number;

  @Prop()
  amount: string;

  @Prop()
  strategy: string;

  @Prop()
  currency: string;

  @Prop()
  nonce: string;

  @Prop()
  startTime: number;

  @Prop()
  endTime: number;

  @Prop()
  minPercentageToAsk: number;

  @Prop()
  params: string[];

  @Prop()
  signature: string;

  @Prop()
  url: string;

  @Prop()
  hash: string;
}

const AuctionSchema = SchemaFactory.createForClass(Auction);

AuctionSchema.index({ tokenId: -1, collectionAddress: -1 });

AuctionSchema.virtual("tokenInfo", {
  type: MongooseSchema.Types.String,
  ref: "Tokens",
  localField: "tokenId",
  foreignField: "tokenId",
  justOne: true,
});

AuctionSchema.virtual("collectionInfo", {
  type: MongooseSchema.Types.String,
  ref: "Collections",
  localField: "collectionAddress",
  foreignField: "collectionAddress",
  justOne: true,
});

AuctionSchema.virtual("fromUserInfo", {
  type: MongooseSchema.Types.String,
  ref: "Users",
  localField: "fromUser",
  foreignField: "address",
  justOne: true,
});

export { AuctionSchema };
