import { Activity } from "../schemas/activity.schema";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

export type NotificationsDocument = Notifications & Document;

@Schema({ timestamps: true })
export class Notifications {
  @Prop()
  address: string;

  @Prop({ default: false })
  read: boolean;

  @Prop({ default: true })
  status: boolean;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: Activity.name,
    default: null,
  })
  activityId: string;
}

const NotificationsSchema = SchemaFactory.createForClass(Notifications);

NotificationsSchema.index({ address: -1, status: -1 });

export { NotificationsSchema };
