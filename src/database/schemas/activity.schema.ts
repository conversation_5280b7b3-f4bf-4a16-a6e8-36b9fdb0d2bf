import { Auction } from "../../database/schemas/auction.schema";
import { constants } from "../../shared/constants/constant";

import { EntityType } from "../../shared/enums/entity-type.enum";
import { ActivityType } from "../enums/activity-type.enum";
import { Document, Schema as MongooseSchema } from "mongoose";
import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";

export type ActivitiesDocument = Activity & Document;

@Schema({ timestamps: true, toJSON: { virtuals: true } })
export class Activity {
  @Prop()
  entityType: EntityType;

  @Prop()
  tokenId: string;

  @Prop()
  collectionAddress: string;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: constants.offer,
    default: null,
  })
  offerId: string;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: Auction.name,
    default: null,
  })
  auctionId: MongooseSchema.Types.ObjectId;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  price: number;

  @Prop({ default: false })
  isSilent: boolean;

  @Prop()
  activityType: ActivityType;

  @Prop()
  fromUser: string;

  @Prop()
  toUser: string;

  @Prop()
  signature: string;

  @Prop()
  hash: string;

  @Prop()
  url: string;
}
const ActivitySchema = SchemaFactory.createForClass(Activity);

ActivitySchema.index({ tokenId: -1, collectionAddress: -1, createdAt: -1, fromUser: -1, toUser: -1 });
ActivitySchema.index({ collectionAddress: 1, isSilent: 1, createdAt: -1 });
ActivitySchema.index({ hash: 1 });

ActivitySchema.virtual("collectionInfo", {
  type: MongooseSchema.Types.String,
  ref: "Collections",
  localField: "collectionAddress",
  foreignField: "collectionAddress",
  justOne: true,
});

ActivitySchema.virtual("tokenInfo", {
  type: MongooseSchema.Types.String,
  ref: "Tokens",
  localField: "tokenId",
  foreignField: "tokenId",
  justOne: true,
});

ActivitySchema.virtual("fromUserInfo", {
  type: MongooseSchema.Types.String,
  ref: "Users",
  localField: "fromUser",
  foreignField: "address",
  justOne: true,
});

ActivitySchema.virtual("toUserInfo", {
  type: MongooseSchema.Types.String,
  ref: "Users",
  localField: "toUser",
  foreignField: "address",
  justOne: true,
});

ActivitySchema.virtual("offerInfo", {
  type: MongooseSchema.Types.ObjectId,
  ref: "Offers",
  localField: "offerId",
  foreignField: "_id",
  justOne: true,
});

export { ActivitySchema };
