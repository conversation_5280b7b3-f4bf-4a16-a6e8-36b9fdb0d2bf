import { constants } from "../../shared/constants/constant";
import { CommonStatus } from "../enums/common-status.enum";
import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

export type StoreFrontDocument = StoreFront & Document;

export class StoreFrontImage {
  @Prop()
  url: string;

  @Prop()
  contentType: string;
}

export class StoreFrontStats {
  @Prop({ default: 0 })
  totalNft: number;

  @Prop({ default: 0 })
  itemChange: number;

  @Prop({ default: 0 })
  totalCollection: number;

  @Prop({ default: 0 })
  collectionChange: number;

  @Prop({ default: 0 })
  visitorCount: number;

  @Prop({ default: 0 })
  visitorChange: number; // TODO Need to find a way to track visitors

  @Prop({ default: 0 })
  totalOffer: number;

  @Prop({ default: 0 })
  offerChange: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  totalVolume?: string;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  totalCommission?: string;

  @Prop({ default: 0 })
  totalTransactions?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  oneDayVolume?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  sevenDayVolume?: number;

  @Prop({ default: 0 })
  oneDayChange?: number;

  @Prop({ default: 0 })
  sevenDayChange?: number;

  @Prop({ default: 0, type: MongooseSchema.Types.Decimal128 })
  thirtyDayVolume?: number;

  @Prop({ default: 0 })
  thirtyDayChange?: number;
}

@Schema({ timestamps: true })
export class StoreFront {
  @Prop()
  name: string;

  @Prop()
  description: string;

  @Prop()
  banner: StoreFrontImage;

  @Prop()
  customLink: string;

  @Prop({ default: CommonStatus.ACTIVE })
  status: CommonStatus;

  @Prop()
  owner: string;

  @Prop({
    default: null,
    description: "Wallet address of the user who has blocked",
  })
  blockedBy: string;

  @Prop({ default: null })
  blockedTime: Date;

  @Prop()
  stats: StoreFrontStats;
}

const StoreFrontSchema = SchemaFactory.createForClass(StoreFront);
StoreFrontSchema.index({ slug: -1 }, { unique: true });

StoreFrontSchema.virtual("userInfo", {
  type: MongooseSchema.Types.String,
  ref: constants.users,
  localField: "owner",
  foreignField: "address",
  justOne: true,
});

StoreFrontSchema.virtual("blockedByUserInfo", {
  type: MongooseSchema.Types.String,
  ref: constants.users,
  localField: "blockedBy",
  foreignField: "address",
  justOne: true,
});

export { StoreFrontSchema };
