import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";

export type CategoriesDocument = Categories & Document;

@Schema({ timestamps: true })
export class Categories {
  @Prop()
  title: string;

  @Prop()
  description: string;

  @Prop()
  slug: string;

  @Prop({ nullable: true })
  coverPicture: string;

  @Prop({ default: true })
  status: boolean;
}

export const CategoriesSchema = SchemaFactory.createForClass(Categories);
