export interface ElasticsearchModuleOptions {
  node: string;
  auth?: {
    username: string;
    password: string;
  };
  maxRetries?: number;
  requestTimeout?: number;
  pingTimeout?: number;
  sniffOnStart?: boolean;
  ssl?: {
    ca?: string;
    rejectUnauthorized?: boolean;
  };
}

export interface ElasticsearchModuleAsyncOptions {
  useFactory: (...args: any[]) => Promise<ElasticsearchModuleOptions> | ElasticsearchModuleOptions;
  inject?: any[];
  imports?: any[];
}
