import * as mappingsConfig from "../mappings/elasticsearch-mappings.json";
import { ElasticsearchService } from "../elasticsearch.service";
import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class CollectionIndexingService {
  private readonly COLLECTION_INDEX: string;

  constructor(private readonly elasticsearchService: ElasticsearchService, private readonly configService: ConfigService) {
    const environment = this.configService.get("NODE_ENV") || "development";
    this.COLLECTION_INDEX = `${environment}_collections`;
  }

  /**
   * Transform MongoDB Collection document to Elasticsearch document
   */
  private transformCollectionForElasticsearch(collection: any): any {
    return {
      _id: collection._id?.toString(),
      elasticsearchId: collection._id?.toString(),
      collectionAddress: collection.address?.toLowerCase(),
      name: collection.name,
      slug: collection.slug,
      logo: collection.logo
        ? {
            url: collection.logo.url,
            contentType: collection.logo.contentType,
          }
        : null,
      isVerified: collection.isVerified || false,
      totalSupply: collection.totalSupply || 0,
      stats: {
        floorPrice: collection.floorPrice ? parseFloat(collection.floorPrice) : 0,
        totalVolume: collection.totalVolume ? parseFloat(collection.totalVolume) : 0,
      },
      status: collection.status,
    };
  }

  /**
   * Index a single collection
   */
  async indexCollection(collection: any): Promise<void> {
    const document = this.transformCollectionForElasticsearch(collection);
    const id = collection._id.toString();

    await this.elasticsearchService.indexDocument(this.COLLECTION_INDEX, id, document);
  }

  /**
   * Bulk index collections
   */
  async bulkIndexCollections(collections: any[]): Promise<void> {
    const operations = collections.flatMap(collection => {
      const document = this.transformCollectionForElasticsearch(collection);
      const id = collection._id.toString();

      return [{ index: { _index: this.COLLECTION_INDEX, _id: id } }, document];
    });

    await this.elasticsearchService.bulkIndex(operations);
  }

  /**
   * Update collection in index
   */
  async updateCollection(collectionId: string, updates: any): Promise<void> {
    await this.elasticsearchService.updateDocument(this.COLLECTION_INDEX, collectionId, updates);
  }

  /**
   * Delete collection from index
   */
  async deleteCollection(collectionId: string): Promise<void> {
    await this.elasticsearchService.deleteDocument(this.COLLECTION_INDEX, collectionId);
  }

  /**
   * Search collections
   */
  async searchCollections(params: { query?: string; filters?: any; sort?: any; from?: number; size?: number }): Promise<any> {
    const { query, filters = {}, sort, from = 0, size = 20 } = params;

    const searchQuery: any = {
      bool: {
        must: [],
        filter: [],
      },
    };

    // Text search
    if (query) {
      searchQuery.bool.must.push({
        multi_match: {
          query,
          fields: ["name^3", "name.keyword^2", "slug"],
          type: "best_fields",
          fuzziness: "AUTO",
        },
      });
    }

    // Apply filters
    if (filters.collectionAddress) {
      searchQuery.bool.filter.push({
        term: { collectionAddress: filters.collectionAddress.toLowerCase() },
      });
    }

    if (filters.isVerified !== undefined) {
      searchQuery.bool.filter.push({
        term: { isVerified: filters.isVerified },
      });
    }

    if (filters.status) {
      searchQuery.bool.filter.push({
        term: { status: filters.status },
      });
    }

    if (filters.volumeRange) {
      searchQuery.bool.filter.push({
        range: {
          "stats.totalVolume": {
            gte: filters.volumeRange.min,
            lte: filters.volumeRange.max,
          },
        },
      });
    }

    if (filters.floorPriceRange) {
      searchQuery.bool.filter.push({
        range: {
          "stats.floorPrice": {
            gte: filters.floorPriceRange.min,
            lte: filters.floorPriceRange.max,
          },
        },
      });
    }

    // Default sort
    const defaultSort = sort || [{ "stats.totalVolume": { order: "desc" } }];

    return this.elasticsearchService.search(this.COLLECTION_INDEX, {
      query: searchQuery,
      sort: defaultSort,
      from,
      size,
      _source: true,
    });
  }

  /**
   * Get collection by address
   */
  async getCollectionByAddress(address: string): Promise<any> {
    const result = await this.elasticsearchService.search(this.COLLECTION_INDEX, {
      query: {
        term: { collectionAddress: address.toLowerCase() },
      },
      size: 1,
    });

    return result.hits?.hits?.[0] || null;
  }

  /**
   * Get trending collections
   */
  async getTrendingCollections(timeRange = "24h", limit = 10): Promise<any> {
    const ranges: any = {
      "24h": "now-1d/d",
      "7d": "now-7d/d",
      "30d": "now-30d/d",
    };

    const query = {
      bool: {
        filter: [
          {
            range: {
              updatedAt: {
                gte: ranges[timeRange] || ranges["24h"],
              },
            },
          },
        ],
      },
    };

    return this.elasticsearchService.search(this.COLLECTION_INDEX, {
      query,
      sort: [{ "stats.totalVolume": { order: "desc" } }],
      from: 0,
      size: limit,
      _source: true,
    });
  }

  /**
   * Create collection index with mappings
   */
  async createCollectionIndex(): Promise<void> {
    const exists = await this.elasticsearchService.indexExists(this.COLLECTION_INDEX);
    if (!exists) {
      await this.elasticsearchService.createIndex(this.COLLECTION_INDEX, mappingsConfig.settings, mappingsConfig.mappings.collections);
    }
  }
}
