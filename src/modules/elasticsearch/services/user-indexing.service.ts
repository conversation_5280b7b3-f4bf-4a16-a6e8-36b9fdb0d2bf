import * as mappingsConfig from "../mappings/elasticsearch-mappings.json";
import { ElasticsearchService } from "../elasticsearch.service";
import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class UserIndexingService {
  private readonly USER_INDEX: string;

  constructor(private readonly elasticsearchService: ElasticsearchService, private readonly configService: ConfigService) {
    const environment = this.configService.get("NODE_ENV") || "development";
    this.USER_INDEX = `${environment}_users`;
  }

  /**
   * Transform MongoDB User document to Elasticsearch document
   */
  private transformUserForElasticsearch(user: any): any {
    return {
      _id: user._id?.toString(),
      elasticsearchId: user._id?.toString(),
      address: user.address?.toLowerCase(),
      name: user.name,
      avatar: user.avatar
        ? {
            url: user.avatar.url,
            contentType: user.avatar.contentType,
          }
        : null,
      isVerified: user.isVerified || false,
      status: user.status,
    };
  }

  /**
   * Index a single user
   */
  async indexUser(user: any): Promise<void> {
    const document = this.transformUserForElasticsearch(user);
    const id = user._id.toString();

    await this.elasticsearchService.indexDocument(this.USER_INDEX, id, document);
  }

  /**
   * Bulk index users
   */
  async bulkIndexUsers(users: any[]): Promise<void> {
    const operations = users.flatMap(user => {
      const document = this.transformUserForElasticsearch(user);
      const id = user._id.toString();

      return [{ index: { _index: this.USER_INDEX, _id: id } }, document];
    });

    await this.elasticsearchService.bulkIndex(operations);
  }

  /**
   * Update user in index
   */
  async updateUser(userId: string, updates: any): Promise<void> {
    await this.elasticsearchService.updateDocument(this.USER_INDEX, userId, updates);
  }

  /**
   * Delete user from index
   */
  async deleteUser(userId: string): Promise<void> {
    await this.elasticsearchService.deleteDocument(this.USER_INDEX, userId);
  }

  /**
   * Search users
   */
  async searchUsers(params: { query?: string; filters?: any; sort?: any; from?: number; size?: number }): Promise<any> {
    const { query, filters = {}, sort, from = 0, size = 20 } = params;

    const searchQuery: any = {
      bool: {
        must: [],
        filter: [],
      },
    };

    // Text search
    if (query) {
      searchQuery.bool.must.push({
        multi_match: {
          query,
          fields: ["name^3", "name.keyword^2", "address"],
          type: "best_fields",
          fuzziness: "AUTO",
        },
      });
    }

    // Apply filters
    if (filters.isVerified !== undefined) {
      searchQuery.bool.filter.push({
        term: { isVerified: filters.isVerified },
      });
    }

    if (filters.status) {
      searchQuery.bool.filter.push({
        term: { status: filters.status },
      });
    }

    if (filters.address) {
      searchQuery.bool.filter.push({
        term: { address: filters.address.toLowerCase() },
      });
    }

    // Default sort
    const defaultSort = sort || [{ _score: { order: "desc" } }];

    return this.elasticsearchService.search(this.USER_INDEX, {
      query: searchQuery,
      sort: defaultSort,
      from,
      size,
      _source: true,
    });
  }

  /**
   * Get user by ID
   */
  async getUserById(userId: string): Promise<any> {
    return this.elasticsearchService.getDocument(this.USER_INDEX, userId);
  }

  /**
   * Get user by address
   */
  async getUserByAddress(address: string): Promise<any> {
    const result = await this.elasticsearchService.search(this.USER_INDEX, {
      query: {
        term: { address: address.toLowerCase() },
      },
      size: 1,
    });

    return result.hits?.hits?.[0] || null;
  }

  /**
   * Create user index with mappings
   */
  async createUserIndex(): Promise<void> {
    const exists = await this.elasticsearchService.indexExists(this.USER_INDEX);
    if (!exists) {
      await this.elasticsearchService.createIndex(this.USER_INDEX, mappingsConfig.settings, mappingsConfig.mappings.users);
    }
  }
}
