import * as mappingsConfig from "../mappings/elasticsearch-mappings.json";
import { ElasticsearchService } from "../elasticsearch.service";
import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class NftIndexingService {
  private readonly NFT_INDEX: string;

  constructor(private readonly elasticsearchService: ElasticsearchService, private readonly configService: ConfigService) {
    const environment = this.configService.get("NODE_ENV") || "development";
    this.NFT_INDEX = `${environment}_tokens`;
  }

  /**
   * Transform MongoDB NFT document to Elasticsearch document
   */
  private transformNFTForElasticsearch(nft: any): any {
    return {
      _id: nft._id?.toString(),
      tokenId: nft.tokenId,
      name: nft.name,
      description: nft.description,
      collectionAddress: nft.collectionAddress?.toLowerCase(),
      collectionInfo: nft.collection
        ? {
            _id: nft.collection._id?.toString(),
            name: nft.collection.name,
            collectionAddress: nft.collection.address?.toLowerCase(),
            isVerified: nft.collection.isVerified || false,
          }
        : null,
      logo: nft.logo
        ? {
            url: nft.logo.url,
            animationUrl: nft.logo.animationUrl,
            contentType: nft.logo.contentType,
          }
        : null,
      status: nft.status,
      isCollectionBlacklisted: nft.isCollectionBlacklisted || false,
      buyNow: nft.buyNow || false,
    };
  }

  /**
   * Index a single NFT
   */
  async indexNFT(nft: any): Promise<void> {
    const document = this.transformNFTForElasticsearch(nft);
    const id = `${nft.collectionAddress}-${nft.tokenId}`;

    await this.elasticsearchService.indexDocument(this.NFT_INDEX, id, document);
  }

  /**
   * Bulk index NFTs
   */
  async bulkIndexNFTs(nfts: any[]): Promise<void> {
    const operations = nfts.flatMap(nft => {
      const document = this.transformNFTForElasticsearch(nft);
      const id = `${nft.collectionAddress}-${nft.tokenId}`;

      return [{ index: { _index: this.NFT_INDEX, _id: id } }, document];
    });

    await this.elasticsearchService.bulkIndex(operations);
  }

  /**
   * Update NFT in index
   */
  async updateNFT(collectionAddress: string, tokenId: string, updates: any): Promise<void> {
    const id = `${collectionAddress}-${tokenId}`;
    await this.elasticsearchService.updateDocument(this.NFT_INDEX, id, updates);
  }

  /**
   * Delete NFT from index
   */
  async deleteNFT(collectionAddress: string, tokenId: string): Promise<void> {
    const id = `${collectionAddress}-${tokenId}`;
    await this.elasticsearchService.deleteDocument(this.NFT_INDEX, id);
  }

  /**
   * Search NFTs
   */
  async searchNFTs(params: { query?: string; filters?: any; sort?: any; from?: number; size?: number }): Promise<any> {
    const { query, filters = {}, sort, from = 0, size = 20 } = params;

    const searchQuery: any = {
      bool: {
        must: [],
        filter: [],
      },
    };

    // Text search
    if (query) {
      searchQuery.bool.must.push({
        multi_match: {
          query,
          fields: ["name^3", "name.keyword^2", "description^2", "collectionInfo.name^2", "tokenId.text"],
          type: "best_fields",
          fuzziness: "AUTO",
        },
      });
    }

    // Apply filters
    if (filters.collectionAddress) {
      searchQuery.bool.filter.push({
        term: { collectionAddress: filters.collectionAddress.toLowerCase() },
      });
    }

    if (filters.owner) {
      searchQuery.bool.filter.push({
        term: { owner: filters.owner.toLowerCase() },
      });
    }

    if (filters.buyNow !== undefined) {
      searchQuery.bool.filter.push({
        term: { buyNow: filters.buyNow },
      });
    }

    if (filters.status) {
      searchQuery.bool.filter.push({
        term: { status: filters.status },
      });
    }

    if (filters.isCollectionBlacklisted !== undefined) {
      searchQuery.bool.filter.push({
        term: { isCollectionBlacklisted: filters.isCollectionBlacklisted },
      });
    }

    if (filters.isVerified !== undefined) {
      searchQuery.bool.filter.push({
        term: { "collectionInfo.isVerified": filters.isVerified },
      });
    }

    // Default sort
    const defaultSort = sort || [{ _score: { order: "desc" } }];

    return this.elasticsearchService.search(this.NFT_INDEX, {
      query: searchQuery,
      sort: defaultSort,
      from,
      size,
      _source: true,
    });
  }

  /**
   * Get NFT by ID
   */
  async getNFTById(collectionAddress: string, tokenId: string): Promise<any> {
    const id = `${collectionAddress}-${tokenId}`;
    return this.elasticsearchService.getDocument(this.NFT_INDEX, id);
  }

  /**
   * Create NFT index with mappings
   */
  async createNFTIndex(): Promise<void> {
    const exists = await this.elasticsearchService.indexExists(this.NFT_INDEX);
    if (!exists) {
      await this.elasticsearchService.createIndex(this.NFT_INDEX, mappingsConfig.settings, mappingsConfig.mappings.tokens);
    }
  }
}
