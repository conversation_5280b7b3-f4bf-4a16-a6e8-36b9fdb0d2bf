import { CollectionIndexingService } from "./collection-indexing.service";
import { NftIndexingService } from "./nft-indexing.service";
import { UserIndexingService } from "./user-indexing.service";
import { Injectable } from "@nestjs/common";

@Injectable()
export class SearchService {
  constructor(
    private readonly nftIndexingService: NftIndexingService,
    private readonly collectionIndexingService: CollectionIndexingService,
    private readonly userIndexingService: UserIndexingService,
  ) {}

  /**
   * Global search across NFTs, Collections, and Users
   */
  async globalSearch(params: { query: string; type?: "nft" | "collection" | "user" | "all"; filters?: any; from?: number; size?: number }): Promise<any> {
    const { query, type = "all", filters = {}, from = 0, size = 20 } = params;

    const results: any = {
      nfts: { hits: [], total: 0 },
      collections: { hits: [], total: 0 },
      users: { hits: [], total: 0 },
    };

    // Search NFTs
    if (type === "nft" || type === "all") {
      const nftResults = await this.nftIndexingService.searchNFTs({
        query,
        filters: filters.nft || {},
        from,
        size,
      });

      results.nfts = {
        hits:
          nftResults.hits?.hits?.map((hit: any) => ({
            ...hit._source,
            _id: hit._id,
            _score: hit._score,
          })) || [],
        total: nftResults.hits?.total?.value || 0,
      };
    }

    // Search Collections
    if (type === "collection" || type === "all") {
      const collectionResults = await this.collectionIndexingService.searchCollections({
        query,
        filters: filters.collection || {},
        from,
        size,
      });

      results.collections = {
        hits:
          collectionResults.hits?.hits?.map((hit: any) => ({
            ...hit._source,
            _id: hit._id,
            _score: hit._score,
          })) || [],
        total: collectionResults.hits?.total?.value || 0,
      };
    }

    // Search Users
    if (type === "user" || type === "all") {
      const userResults = await this.userIndexingService.searchUsers({
        query,
        filters: filters.user || {},
        from,
        size,
      });

      results.users = {
        hits:
          userResults.hits?.hits?.map((hit: any) => ({
            ...hit._source,
            _id: hit._id,
            _score: hit._score,
          })) || [],
        total: userResults.hits?.total?.value || 0,
      };
    }

    return results;
  }

  /**
   * Autocomplete suggestions
   */
  async getAutocompleteSuggestions(params: { query: string; type?: "nft" | "collection" | "user" | "all"; size?: number }): Promise<any> {
    const { query, type = "all", size = 10 } = params;

    const suggestions: any = {
      nfts: [],
      collections: [],
      users: [],
    };

    // Get NFT suggestions
    if (type === "nft" || type === "all") {
      const nftResults = await this.nftIndexingService.searchNFTs({
        query,
        from: 0,
        size,
      });

      suggestions.nfts =
        nftResults.hits?.hits?.map((hit: any) => ({
          id: hit._id,
          name: hit._source.name,
          logo: hit._source.logo,
          collectionName: hit._source.collectionInfo?.name,
          type: "nft",
        })) || [];
    }

    // Get Collection suggestions
    if (type === "collection" || type === "all") {
      const collectionResults = await this.collectionIndexingService.searchCollections({
        query,
        from: 0,
        size,
      });

      suggestions.collections =
        collectionResults.hits?.hits?.map((hit: any) => ({
          id: hit._id,
          name: hit._source.name,
          logo: hit._source.logo,
          isVerified: hit._source.isVerified,
          type: "collection",
        })) || [];
    }

    // Get User suggestions
    if (type === "user" || type === "all") {
      const userResults = await this.userIndexingService.searchUsers({
        query,
        from: 0,
        size,
      });

      suggestions.users =
        userResults.hits?.hits?.map((hit: any) => ({
          id: hit._id,
          name: hit._source.name,
          address: hit._source.address,
          avatar: hit._source.avatar,
          isVerified: hit._source.isVerified,
          type: "user",
        })) || [];
    }

    return suggestions;
  }

  /**
   * Get trending searches
   */
  async getTrendingSearches(limit = 10): Promise<any> {
    // This would typically be implemented with a separate index tracking search queries
    // For now, return trending collections as a proxy
    const trendingCollections = await this.collectionIndexingService.getTrendingCollections("24h", limit);

    return {
      trending:
        trendingCollections.hits?.hits?.map((hit: any) => ({
          term: hit._source.name,
          type: "collection",
          count: hit._source.stats?.totalVolume || 0,
        })) || [],
    };
  }

  /**
   * Advanced search with facets
   */
  async advancedSearch(params: { query?: string; type: "nft" | "collection" | "user"; filters?: any; facets?: string[]; from?: number; size?: number }): Promise<any> {
    const { query, type, filters = {}, facets = [], from = 0, size = 20 } = params;

    if (type === "nft") {
      const searchParams: any = {
        query,
        filters,
        from,
        size,
      };

      // Add aggregations for facets
      if (facets.length > 0) {
        const aggs: any = {};

        facets.forEach(facet => {
          switch (facet) {
            case "collections":
              aggs.collections = {
                terms: {
                  field: "collectionAddress",
                  size: 50,
                },
                aggs: {
                  collection_name: {
                    top_hits: {
                      size: 1,
                      _source: ["collectionInfo.name"],
                    },
                  },
                },
              };
              break;
            case "status":
              aggs.status = {
                terms: {
                  field: "status",
                  size: 10,
                },
              };
              break;
            case "buyNow":
              aggs.buyNow = {
                terms: {
                  field: "buyNow",
                },
              };
              break;
          }
        });

        searchParams.aggs = aggs;
      }

      return this.nftIndexingService.searchNFTs(searchParams);
    } else if (type === "collection") {
      const searchParams: any = {
        query,
        filters,
        from,
        size,
      };

      // Add aggregations for facets
      if (facets.length > 0) {
        const aggs: any = {};

        facets.forEach(facet => {
          switch (facet) {
            case "volumeRange":
              aggs.volumeRange = {
                range: {
                  field: "stats.totalVolume",
                  ranges: [{ to: 100 }, { from: 100, to: 1000 }, { from: 1000, to: 10000 }, { from: 10000, to: 100000 }, { from: 100000 }],
                },
              };
              break;
            case "verified":
              aggs.verified = {
                terms: {
                  field: "isVerified",
                },
              };
              break;
            case "status":
              aggs.status = {
                terms: {
                  field: "status",
                  size: 10,
                },
              };
              break;
          }
        });

        searchParams.aggs = aggs;
      }

      return this.collectionIndexingService.searchCollections(searchParams);
    } else {
      // User search
      const searchParams: any = {
        query,
        filters,
        from,
        size,
      };

      // Add aggregations for facets
      if (facets.length > 0) {
        const aggs: any = {};

        facets.forEach(facet => {
          switch (facet) {
            case "verified":
              aggs.verified = {
                terms: {
                  field: "isVerified",
                },
              };
              break;
            case "status":
              aggs.status = {
                terms: {
                  field: "status",
                  size: 10,
                },
              };
              break;
          }
        });

        searchParams.aggs = aggs;
      }

      return this.userIndexingService.searchUsers(searchParams);
    }
  }

  /**
   * Similar items search
   */
  async findSimilarItems(params: { type: "nft" | "collection" | "user"; id: string; size?: number }): Promise<any> {
    const { type, id, size = 10 } = params;

    if (type === "nft") {
      // Get the NFT details first
      const [collectionAddress, tokenId] = id.split("-");
      const nft = await this.nftIndexingService.getNFTById(collectionAddress, tokenId);

      if (!nft._source) {
        return { hits: [], total: 0 };
      }

      // Search for similar NFTs
      const similarResults = await this.nftIndexingService.searchNFTs({
        filters: {
          collectionAddress: nft._source.collectionAddress,
        },
        from: 0,
        size: size + 1, // Get one extra to exclude the current item
      });

      // Filter out the current item
      const hits = similarResults.hits?.hits?.filter((hit: any) => hit._id !== id) || [];

      return {
        hits: hits.slice(0, size).map((hit: any) => ({
          ...hit._source,
          _id: hit._id,
          _score: hit._score,
        })),
        total: Math.max(0, (similarResults.hits?.total?.value || 0) - 1),
      };
    } else if (type === "collection") {
      // For collections, find similar ones based on verification status and volume range
      const collection = await this.collectionIndexingService.getCollectionByAddress(id);

      if (!collection || !collection._source) {
        return { hits: [], total: 0 };
      }

      // Search for similar collections based on verification status
      const similarResults = await this.collectionIndexingService.searchCollections({
        filters: {
          isVerified: collection._source.isVerified,
        },
        from: 0,
        size: size + 1, // Get one extra to exclude the current item
      });

      // Filter out the current item
      const hits = similarResults.hits?.hits?.filter((hit: any) => hit._id !== id) || [];

      return {
        hits: hits.slice(0, size).map((hit: any) => ({
          ...hit._source,
          _id: hit._id,
          _score: hit._score,
        })),
        total: Math.max(0, (similarResults.hits?.total?.value || 0) - 1),
      };
    } else {
      // Get similar users (e.g., by verification status)
      const user = await this.userIndexingService.getUserById(id);

      if (!user._source) {
        return { hits: [], total: 0 };
      }

      // Search for similar users
      const similarResults = await this.userIndexingService.searchUsers({
        filters: {
          isVerified: user._source.isVerified,
        },
        from: 0,
        size: size + 1,
      });

      // Filter out the current item
      const hits = similarResults.hits?.hits?.filter((hit: any) => hit._id !== id) || [];

      return {
        hits: hits.slice(0, size).map((hit: any) => ({
          ...hit._source,
          _id: hit._id,
          _score: hit._score,
        })),
        total: Math.max(0, (similarResults.hits?.total?.value || 0) - 1),
      };
    }
  }
}
