import { ElasticsearchClient, elasticsearchModuleOptions } from "./elasticsearch.constants";
import { ElasticsearchModuleAsyncOptions, ElasticsearchModuleOptions } from "./elasticsearch-module-options";
import { DynamicModule, Global, Module, Provider } from "@nestjs/common";
import { Client } from "@elastic/elasticsearch";

@Global()
@Module({})
export class ElasticsearchCoreModule {
  static forRoot(options: ElasticsearchModuleOptions): DynamicModule {
    const elasticsearchClientProvider: Provider = {
      provide: ElasticsearchClient,
      useValue: new Client(options),
    };

    return {
      module: ElasticsearchCoreModule,
      providers: [elasticsearchClientProvider],
      exports: [elasticsearchClientProvider],
    };
  }

  static forRootAsync(options: ElasticsearchModuleAsyncOptions): DynamicModule {
    const elasticsearchClientProvider: Provider = {
      provide: ElasticsearchClient,
      useFactory: async (elasticsearchOptions: ElasticsearchModuleOptions) => {
        return new Client(elasticsearchOptions);
      },
      inject: [elasticsearchModuleOptions],
    };

    const asyncProviders = this.createAsyncProviders(options);

    return {
      module: ElasticsearchCoreModule,
      imports: options.imports || [],
      providers: [...asyncProviders, elasticsearchClientProvider],
      exports: [elasticsearchClientProvider],
    };
  }

  private static createAsyncProviders(options: ElasticsearchModuleAsyncOptions): Provider[] {
    return [
      {
        provide: elasticsearchModuleOptions,
        useFactory: options.useFactory,
        inject: options.inject || [],
      },
    ];
  }
}
