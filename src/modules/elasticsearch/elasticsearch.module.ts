import { ElasticsearchCoreModule } from "./elasticsearch-core.module";
import { ElasticsearchModuleAsyncOptions, ElasticsearchModuleOptions } from "./elasticsearch-module-options";
import { ElasticsearchService } from "./elasticsearch.service";
import { NftIndexingService } from "./services/nft-indexing.service";
import { CollectionIndexingService } from "./services/collection-indexing.service";
import { UserIndexingService } from "./services/user-indexing.service";
import { SearchService } from "./services/search.service";
import { DynamicModule, Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

@Module({
  imports: [ConfigModule],
  providers: [ElasticsearchService, NftIndexingService, CollectionIndexingService, UserIndexingService, SearchService],
  exports: [ElasticsearchService, NftIndexingService, CollectionIndexingService, UserIndexingService, SearchService],
})
export class ElasticsearchModule {
  static forRoot(options: ElasticsearchModuleOptions): DynamicModule {
    return {
      module: ElasticsearchModule,
      imports: [ElasticsearchCoreModule.forRoot(options)],
    };
  }

  static forRootAsync(options: ElasticsearchModuleAsyncOptions): DynamicModule {
    return {
      module: ElasticsearchModule,
      imports: [ElasticsearchCoreModule.forRootAsync(options)],
    };
  }
}
