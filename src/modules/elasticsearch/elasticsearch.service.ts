import { ElasticsearchClient } from "./elasticsearch.constants";
import { Injectable, Inject } from "@nestjs/common";
import { Client } from "@elastic/elasticsearch";

@Injectable()
export class ElasticsearchService {
  constructor(@Inject(ElasticsearchClient) private readonly elasticsearchClient: Client) {}

  /**
   * Get the Elasticsearch client instance
   */
  getClient(): Client {
    return this.elasticsearchClient;
  }

  /**
   * Create an index with optional settings and mappings
   */
  async createIndex(index: string, settings?: any, mappings?: any): Promise<any> {
    const params: any = { index };
    if (settings) {
      params.settings = settings;
    }
    if (mappings) {
      params.mappings = mappings;
    }
    return this.elasticsearchClient.indices.create(params);
  }

  /**
   * Check if an index exists
   */
  async indexExists(index: string): Promise<boolean> {
    return await this.elasticsearchClient.indices.exists({ index });
  }

  /**
   * Delete an index
   */
  async deleteIndex(index: string): Promise<any> {
    return this.elasticsearchClient.indices.delete({ index });
  }

  /**
   * Index a document
   */
  async indexDocument(index: string, id: string, document: any): Promise<any> {
    return this.elasticsearchClient.index({
      index,
      id,
      document: document,
    });
  }

  /**
   * Bulk index documents
   */
  async bulkIndex(operations: any[]): Promise<any> {
    return this.elasticsearchClient.bulk({
      refresh: true,
      operations: operations,
    });
  }

  /**
   * Update a document
   */
  async updateDocument(index: string, id: string, document: any): Promise<any> {
    return this.elasticsearchClient.update({
      index,
      id,
      doc: document,
    });
  }

  /**
   * Get a document by ID
   */
  async getDocument(index: string, id: string): Promise<any> {
    return this.elasticsearchClient.get({
      index,
      id,
    });
  }

  /**
   * Delete a document
   */
  async deleteDocument(index: string, id: string): Promise<any> {
    return this.elasticsearchClient.delete({
      index,
      id,
    });
  }

  /**
   * Search documents
   */
  async search(index: string, query: any): Promise<any> {
    return this.elasticsearchClient.search({
      index,
      query: query.query,
      sort: query.sort,
      from: query.from,
      size: query.size,
      _source: query._source,
      aggs: query.aggs,
    });
  }

  /**
   * Refresh an index
   */
  async refreshIndex(index: string): Promise<any> {
    return this.elasticsearchClient.indices.refresh({ index });
  }

  /**
   * Count documents in an index
   */
  async count(index: string, query?: any): Promise<any> {
    const params: any = { index };
    if (query) {
      params.query = query;
    }
    return this.elasticsearchClient.count(params);
  }

  /**
   * Create or update index mappings
   */
  async putMapping(index: string, mapping: any): Promise<any> {
    return this.elasticsearchClient.indices.putMapping({
      index,
      ...mapping,
    });
  }

  /**
   * Create or update index settings
   */
  async putSettings(index: string, settings: any): Promise<any> {
    return this.elasticsearchClient.indices.putSettings({
      index,
      ...settings,
    });
  }
}
