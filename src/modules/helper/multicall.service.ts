import { royaltyAbi } from "../../blockchain/abi/royalty.abi";
import { collectionAbi } from "../../blockchain/abi/collection.abi";
import { Web3Instance } from "../../blockchain/web3Instance";

import { Logger } from "winston";
import { ConfigService } from "@nestjs/config";
import { Inject, Injectable, OnModuleInit } from "@nestjs/common";
import { WINSTON_MODULE_PROVIDER } from "nest-winston";
import { Multicall, ContractCallResults, ContractCallContext } from "ethereum-multicall";

interface ContractCall {
  reference?: string;
  contractAddress: string;
  methodCalls: {
    reference: string;
    methodName: string;
    methodParameters: any[];
  }[];
}

@Injectable()
class MulticallService implements OnModuleInit {
  private multicall: Multicall;

  constructor(@Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger, private configService: ConfigService, private instance: Web3Instance) {}

  async onModuleInit() {
    const rpc = this.instance.getRPC();

    // DEBUGGING
    // console.log({ RPC: rpc, CONTRACT_ADDRESS: this.configService.get("MULTICALL_MAIN") });

    this.multicall = new Multicall({
      multicallCustomContractAddress: this.configService.get("MULTICALL_MAIN"),
      nodeUrl: rpc,
      tryAggregate: true,
    });
  }

  /**
   * Performs a multicall with flexible contract and method configuration
   * @param contractCalls Array of contract call configurations
   * @returns Promise resolving to contract call results
   */
  async performMulticall(contractCalls: ContractCall[], collection = true): Promise<ContractCallResults> {
    try {
      // Transform ContractCall to ContractCallContext
      const contractCallContext: ContractCallContext[] = contractCalls.map(call => ({
        reference: call?.reference ? call.reference : call.contractAddress,
        contractAddress: call.contractAddress,
        abi: collection ? collectionAbi : royaltyAbi,
        calls: call.methodCalls.map(methodCall => ({
          reference: methodCall.reference,
          methodName: methodCall.methodName,
          methodParameters: methodCall.methodParameters,
        })),
      }));

      // Execute multicall
      if (contractCallContext.length) {
        const results = await this.multicall.call(contractCallContext);
        return results;
      } else {
        return { results: {}, blockNumber: 0 };
      }
    } catch (error) {
      this.logger.error(`Multicall Error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Helper method to parse results from a specific contract call
   * @param results Full multicall results
   * @param contractReference Reference of the contract in the multicall
   * @param methodReference Reference of the method call
   * @returns Parsed result or null
   */
  parseResult(response: ContractCallResults, contractReference: string, methodReference: string): any {
    try {
      const result = response.results[contractReference].callsReturnContext.find(call => call.reference === methodReference && call.success).returnValues;
      return result[1] ? result : result[0];
    } catch {
      return null;
    }
  }
}

export { MulticallService, ContractCall };
