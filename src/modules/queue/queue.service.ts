import { BlockData, BlockDataDocument } from "../../database/schemas/block-data.schema";
import { Model } from "mongoose";
import { Injectable } from "@nestjs/common";
import { createLogger, format, transports } from "winston";
import { InjectModel } from "@nestjs/mongoose";

@Injectable()
export class QueueService {
  private logger: any;

  constructor(
    @InjectModel(BlockData.name)
    private blockDataModel: Model<BlockDataDocument>,
  ) {
    // Create a Winston logger
    this.logger = createLogger({
      level: "info",
      format: format.combine(format.timestamp(), format.json()),
      transports: [new transports.Console()],
    });
  }

  async saveCollectionAndToken(collectionResult, tokenResult) {
    try {
      const operations = [
        ...collectionResult.map(doc => ({
          insertOne: { document: doc },
        })),
        ...tokenResult.map(doc => ({
          insertOne: { document: doc },
        })),
      ];

      await this.blockDataModel.bulkWrite(operations, { ordered: false });
    } catch (error) {
      this.logger.error(`Error while saving data in blockdata ${error?.message}`);
    }
  }
}
