import { QueueService } from "./queue.service";
import { BlockData, BlockDataSchema } from "../../database/schemas/block-data.schema";
import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";

@Module({
  imports: [MongooseModule.forFeature([{ name: BlockData.name, schema: BlockDataSchema }])],
  providers: [QueueService],
  exports: [QueueService],
})
export class QueueModule {}
