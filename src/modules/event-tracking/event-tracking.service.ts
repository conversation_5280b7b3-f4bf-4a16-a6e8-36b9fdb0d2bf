import { Web3Instance } from "../../blockchain/web3Instance";
import { Collections, CollectionsDocument } from "../../database/schemas/collections.schema";
import { Tokens, TokensDocument } from "../../database/schemas/tokens.schema";
import { ActivitiesDocument, Activity } from "../../database/schemas/activity.schema";
import { Status } from "../../shared/enums/status.enum";
import { StatisticsUpdateService } from "../../shared/Providers/statistics-update.service";
import { Users, UsersDocument } from "../../database/schemas/users.schema";
import { CommonStatus } from "../../database/enums/common-status.enum";
import { EntityType } from "../../shared/enums/entity-type.enum";
import { ActivityType } from "../../database/enums/activity-type.enum";
import { BlockData, BlockDataDocument } from "../../database/schemas/block-data.schema";
import { ResourceMonitorService } from "../../shared/Providers/resource-monitor.service";
import { ProcessMonitorService } from "../../shared/Providers/process-monitor.service";
import { ConfigService } from "@nestjs/config";
import { Cron } from "@nestjs/schedule";
import mongoose, { Model } from "mongoose";
import { InjectModel } from "@nestjs/mongoose";
import moment from "moment";
import { Logger } from "winston";
import { WINSTON_MODULE_PROVIDER } from "nest-winston";
import { Cache } from "cache-manager";

import { CACHE_MANAGER, Inject, Injectable } from "@nestjs/common";
import * as os from "os";
import { Worker } from "worker_threads";
import * as path from "path";

@Injectable()
export class EventTrackingService {
  private readonly NUM_WORKERS = 4; // Math.ceil(os.cpus().length / 2);
  private readonly BATCH_SIZE = 15;
  private isRunning = false;
  private taskQueue: number[] = [];

  private previousBlock = 15197446;
  private cronStartedTime: any;

  private MAX_BLOCKS_PER_CRON = this.BATCH_SIZE * this.NUM_WORKERS;

  private HARD_START: boolean;

  etherInstance: any;
  environment: any;

  // Memory monitoring properties
  private memoryMonitorInterval: NodeJS.Timeout;
  private readonly MEMORY_WARNING_THRESHOLD = 1024 * 1024 * 1024; // 1GB
  private readonly MEMORY_CRITICAL_THRESHOLD = 1.5 * 1024 * 1024 * 1024; // 1.5GB

  // Memory monitoring
  private lastMemoryLogTime = 0;

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private statisticsUpdateService: StatisticsUpdateService,
    private web3InstanceModule: Web3Instance,
    private configService: ConfigService,
    private resourceMonitor: ResourceMonitorService,
    private processMonitor: ProcessMonitorService,
    @InjectModel(Collections.name)
    private collectionModel: Model<CollectionsDocument>,
    @InjectModel(Tokens.name)
    private tokenModel: Model<TokensDocument>,
    @InjectModel(Activity.name)
    private activityModel: Model<ActivitiesDocument>,
    @InjectModel(Users.name)
    private userModel: Model<UsersDocument>,

    @InjectModel(BlockData.name)
    private blockDataModel: Model<BlockDataDocument>,
  ) {
    this.HARD_START = this.configService.get("HARD_START");
    this.environment = this.configService.get("NODE_ENV");
    this.etherInstance = this.web3InstanceModule.getEtherInstance();

    // Initialize memory monitoring
    this.initializeMemoryMonitoring();

    // Initialize process signal handlers
    this.initializeProcessHandlers();

    // Start comprehensive resource monitoring
    this.resourceMonitor.startMonitoring(30000); // Monitor every 30 seconds

    this.logger.info(`EventTrackingService initialized with ${this.NUM_WORKERS} workers (half of ${os.cpus().length} available cores)`);
  }

  // Clean up method for graceful shutdown
  async onModuleDestroy() {
    this.logger.info("EventTrackingService: Starting graceful shutdown...");
    // Stop memory monitoring
    if (this.memoryMonitorInterval) {
      clearInterval(this.memoryMonitorInterval);
    }

    // Stop resource monitoring
    this.resourceMonitor.stopMonitoring();

    // Use ProcessMonitorService for graceful shutdown
    await this.processMonitor.gracefulShutdown();

    this.logger.info("EventTrackingService: Graceful shutdown completed");
  }



  private initializeMemoryMonitoring() {
    // Monitor memory every 30 seconds
    this.memoryMonitorInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, 30000);

    // Also monitor on process exit
    process.on("exit", () => {
      if (this.memoryMonitorInterval) {
        clearInterval(this.memoryMonitorInterval);
      }
    });
  }

  private initializeProcessHandlers() {
    // Handle graceful shutdown signals
    const gracefulShutdown = async (signal: string) => {
      this.logger.info(`Received ${signal}, initiating graceful shutdown...`);

      try {
        // Stop accepting new cron jobs
        this.isRunning = true; // Prevent new cron jobs from starting

        // Use ProcessMonitorService for graceful shutdown
        await this.processMonitor.gracefulShutdown(signal);

        this.logger.info(`Graceful shutdown completed for signal ${signal}`);
        process.exit(0);
      } catch (error) {
        this.logger.error(`Error during graceful shutdown: ${error.message}`);
        process.exit(1);
      }
    };

    // Register signal handlers
    process.on("SIGTERM", () => void gracefulShutdown("SIGTERM"));
    process.on("SIGINT", () => void gracefulShutdown("SIGINT"));

    // Handle uncaught exceptions
    process.on("uncaughtException", error => {
      this.logger.error("Uncaught Exception:", error);
      void gracefulShutdown("uncaughtException");
    });

    // Handle unhandled promise rejections
    process.on("unhandledRejection", (reason, promise) => {
      this.logger.error("Unhandled Rejection at:", promise, "reason:", reason);
      void gracefulShutdown("unhandledRejection");
    });
  }

  private checkMemoryUsage() {
    const memUsage = process.memoryUsage();
    const systemMemory = {
      total: os.totalmem(),
      free: os.freemem(),
      used: os.totalmem() - os.freemem(),
    };

    // Convert to MB for readability
    const memoryInfo = {
      process: {
        rss: Math.round((memUsage.rss / 1024 / 1024) * 100) / 100,
        heapTotal: Math.round((memUsage.heapTotal / 1024 / 1024) * 100) / 100,
        heapUsed: Math.round((memUsage.heapUsed / 1024 / 1024) * 100) / 100,
        external: Math.round((memUsage.external / 1024 / 1024) * 100) / 100,
        arrayBuffers: Math.round((memUsage.arrayBuffers / 1024 / 1024) * 100) / 100,
      },
      system: {
        total: Math.round((systemMemory.total / 1024 / 1024) * 100) / 100,
        free: Math.round((systemMemory.free / 1024 / 1024) * 100) / 100,
        used: Math.round((systemMemory.used / 1024 / 1024) * 100) / 100,
        usedPercent: Math.round((systemMemory.used / systemMemory.total) * 100),
      },
    };

    // Log memory usage (info level every 5 minutes, debug level every 30 seconds)
    const now = Date.now();
    if (!this.lastMemoryLogTime || now - this.lastMemoryLogTime > 300000) {
      // 5 minutes
      this.logger.info(
        `Memory Usage - Process RSS: ${memoryInfo.process.rss}MB, Heap: ${memoryInfo.process.heapUsed}/${memoryInfo.process.heapTotal}MB, System: ${memoryInfo.system.usedPercent}% (${memoryInfo.system.used}/${memoryInfo.system.total}MB), Active Workers: ${this.processMonitor.getActiveWorkerCount()}/${this.NUM_WORKERS}`,
      );
      this.lastMemoryLogTime = now;
    }

    // Check for memory warnings
    if (memUsage.rss > this.MEMORY_CRITICAL_THRESHOLD) {
      this.logger.error(`🚨 CRITICAL MEMORY USAGE: ${memoryInfo.process.rss}MB RSS - Process may be killed by system!`);

      // Force garbage collection
      if (global.gc) {
        this.logger.warn("Forcing garbage collection due to high memory usage");
        global.gc();
      }

      // If we're in a cron job, log additional context
      if (this.isRunning) {
        this.logger.error(`Critical memory usage during cron job - Current block: ${this.previousBlock}, Active workers: ${this.NUM_WORKERS}`);
      }
    } else if (memUsage.rss > this.MEMORY_WARNING_THRESHOLD) {
      this.logger.warn(`⚠️ High memory usage detected: ${memoryInfo.process.rss}MB RSS`);
    }

    // Check system memory
    if (memoryInfo.system.usedPercent > 90) {
      this.logger.error(`🚨 CRITICAL SYSTEM MEMORY: ${memoryInfo.system.usedPercent}% used (${memoryInfo.system.free}MB free)`);
    } else if (memoryInfo.system.usedPercent > 80) {
      this.logger.warn(`⚠️ High system memory usage: ${memoryInfo.system.usedPercent}% used`);
    }

    // Check for worker leaks using ProcessMonitorService
    const activeWorkerCount = this.processMonitor.getActiveWorkerCount();
    if (activeWorkerCount > this.NUM_WORKERS) {
      this.logger.error(`🚨 WORKER LEAK DETECTED: ${activeWorkerCount} active workers exceeds limit of ${this.NUM_WORKERS}`);
    }
  }

  private logMemorySnapshot(context: string) {
    const memUsage = process.memoryUsage();
    const memoryMB = {
      rss: Math.round((memUsage.rss / 1024 / 1024) * 100) / 100,
      heapTotal: Math.round((memUsage.heapTotal / 1024 / 1024) * 100) / 100,
      heapUsed: Math.round((memUsage.heapUsed / 1024 / 1024) * 100) / 100,
      external: Math.round((memUsage.external / 1024 / 1024) * 100) / 100,
    };

    this.logger.info(`Memory snapshot [${context}] - RSS: ${memoryMB.rss}MB, Heap: ${memoryMB.heapUsed}/${memoryMB.heapTotal}MB, External: ${memoryMB.external}MB`);
  }

  /**
   * Initializes the event tracking service by starting a cron job to traverse blocks.
   *
   * This method checks if a previous cron job is still running by inspecting the `lock` property.
   * If a job is already running, it logs a message and exits. Otherwise, it sets the `lock` to true,
   * logs the start time, retrieves the previous block number from the cache or configuration, and
   * starts the block synchronization process.
   *
   * Upon completion or error, it logs the completion time or error message respectively, and releases the lock.
   *
   * @returns {Promise<void>} A promise that resolves when the initialization process is complete.
   *
   * @throws {Error} If an error occurs during the block synchronization process, it is caught and logged.
   *
   * @example
   * ```typescript
   * const eventTrackingService = new EventTrackingService();
   * await eventTrackingService.init();
   * ```
   */
  @Cron("*/10 * * * * *")
  async init() {
    if (this.isRunning) {
      this.logger.info(`******** Cron did not start since cron started at ${this.cronStartedTime} is still running for block ${this.previousBlock}. ********`);
      return;
    }

    try {
      const startedTime = moment().format();
      this.logger.info(`******************* Block traverse Cron started at ${startedTime} ****************`);
      this.cronStartedTime = startedTime;
      this.isRunning = true;

      // Log memory at start of cron
      this.logMemorySnapshot("Cron Start");

      // Check system memory before proceeding
      const systemMemory = {
        total: os.totalmem(),
        free: os.freemem(),
        used: os.totalmem() - os.freemem(),
        usedPercent: Math.round(((os.totalmem() - os.freemem()) / os.totalmem()) * 100),
      };

      // If system memory is critically low, skip this cron run
      if (systemMemory.usedPercent > 95) {
        this.logger.error(`🚨 CRITICAL SYSTEM MEMORY: ${systemMemory.usedPercent}% used (${Math.round(systemMemory.free / 1024 / 1024)}MB free)`);
        this.logger.warn("Skipping cron run due to critical memory usage");
        return;
      }

      // Get the starting block
      this.previousBlock = (await this.cacheManager.get<number>("blockNo")) || Number(this.configService.get("START_BLOCK"));

      const newForkBlock = await this.cacheManager.get<number>("forkBlock");
      if (newForkBlock) {
        this.previousBlock = newForkBlock;
        await this.cacheManager.del("forkBlock");
      } else if (JSON.parse(this.HARD_START.toString())) {
        this.previousBlock = Number(this.configService.get("START_BLOCK")); // Starting block - 17232999
        this.HARD_START = false;
      }

      const currentBlock = await this.etherInstance.getBlockNumber();
      const targetBlock = Math.min(currentBlock, this.previousBlock + this.MAX_BLOCKS_PER_CRON);

      // Prepare task queue
      this.taskQueue = [];
      for (let block = this.previousBlock + 1; block <= targetBlock; block += this.BATCH_SIZE) {
        this.taskQueue.push(block);
      }

      // Adjust worker count based on system memory and task queue size
      let adjustedWorkers = Math.min(this.NUM_WORKERS, this.taskQueue.length);
      if (systemMemory.usedPercent > 85) {
        adjustedWorkers = Math.max(1, Math.floor(adjustedWorkers / 2));
        this.logger.warn(`High system memory usage (${systemMemory.usedPercent}%), reducing workers to ${adjustedWorkers}`);
      }

      console.log({
        targetBlock,
        prev: this.previousBlock,
        currentBlock,
        workers: adjustedWorkers,
        originalWorkers: this.NUM_WORKERS,
        max: this.MAX_BLOCKS_PER_CRON,
        len: this.taskQueue.length,
        availableCores: os.cpus().length,
        systemMemoryGB: Math.round((os.totalmem() / 1024 / 1024 / 1024) * 100) / 100,
        systemMemoryUsed: systemMemory.usedPercent,
      });

      // Log memory before starting worker pool
      this.logMemorySnapshot("Before Worker Pool");

      await this.runWorkerPool(adjustedWorkers);

      // Log memory after worker pool completion
      this.logMemorySnapshot("After Worker Pool");

      this.logger.info(`******************* Cron completed at ${moment().format()} ****************`);
    } catch (error) {
      this.logger.error(`init ${error?.message}`);
    } finally {
      this.isRunning = false;
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
        this.logMemorySnapshot("After GC");
      }
    }
  }

  private async runWorkerPool(workerCount?: number) {
    const pool: Promise<void>[] = [];
    const lastBlocks: number[] = [];
    const effectiveWorkerCount = workerCount || this.NUM_WORKERS;

    for (let i = 0; i < Math.min(effectiveWorkerCount, this.taskQueue.length); i++) {
      // TODO: Keep track of the worker status (success/failed) in redis
      // If a worker fails, we can restart it
      pool.push(
        (async () => {
          while (true) {
            const blockStart = this.taskQueue.shift();
            if (blockStart === undefined) return;

            try {
              // Log memory before worker task
              if (i === 0) {
                // Only log for first worker to avoid spam
                this.logMemorySnapshot(`Worker ${i + 1} Start - Block ${blockStart}`);
              }

              const workerResult = await this.runWorkerTask(blockStart, i);
              const { blockNumber } = workerResult;

              lastBlocks.push(blockNumber);
              this.logger.info(`Worker ${i + 1} finished processing blocks ${blockStart} to ${blockStart + this.BATCH_SIZE - 1}`);

              // Log memory after worker task (for first worker only)
              if (i === 0) {
                this.logMemorySnapshot(`Worker ${i + 1} Complete - Block ${blockNumber}`);
              }
            } catch (error) {
              this.logger.error(`Worker ${i + 1} failed on block ${blockStart} after retries: ${error.message}`);
              // Log memory on worker error
              this.logMemorySnapshot(`Worker ${i + 1} Error - Block ${blockStart}`);
              // Continue with next block even if this worker fails
              lastBlocks.push(blockStart + this.BATCH_SIZE - 1);
            }
          }
        })(),
      );
    }

    await Promise.all(pool);

    // Update Redis with the latest processed block
    const lastBlock = Math.max(...lastBlocks);
    if (lastBlock) {
      this.logger.info(`Updating Redis with last processed block: ${lastBlock}`);
      await this.cacheManager.set("blockNo", lastBlock, { ttl: 0 });
    }
  }



  private runWorkerTask(blockStart: number, workerId: number): Promise<{ workerId: number; collections: any[]; tokens: any[]; blockNumber: number }> {
    return this.processMonitor.createMonitoredWorker(
      path.resolve(__dirname, "./worker.js"),
      {
        blockStart,
        blockSize: this.BATCH_SIZE,
        workerId,
        dbUri: this.configService.get("MONGO_DB_URI"),
      },
      workerId,
      3, // maxRetries
      180000 // 3 minutes timeout
    );
  }



  /**
   * @method updateCollectionDataFromBlockData
   * This method updates the collection data from block data. It fetches the collection block data from the database,
   * checks if the collection data already exists in the collection model, and updates or inserts the data accordingly.
   * It also updates the owner as an artist and deletes the inserted collection from the block data collection.
   *
   * @async
   * @returns {Promise<void>} A promise that resolves when the operation is complete.
   *
   * @throws Will log an error message if there is an issue while saving collection data in block data.
   *
   * @example
   * ```typescript
   * await this.updateCollectionDataFromBlockData();
   * ```
   */
  @Cron("0 */3 * * * *")
  async updateCollectionDataFromBlockData() {
    try {
      this.logger.info(`******************* Collection insert cron started at ${moment().format()} ****************`);
      const collectionBlockDatas = await this.blockDataModel.find({ type: EntityType.COLLECTION }).sort({ blockNumber: 1 }).limit(500).lean();
      if (collectionBlockDatas.length > 0) {
        const collectionAddress = collectionBlockDatas.map(collection => collection.collectionData.collectionAddress);
        const collectionOldInfo = await this.collectionModel
          .find({ collectionAddress: { $in: collectionAddress } })
          .select({ collectionAddress: 1 })
          .lean();
        const presentCollectionMap = {};
        const deleteCollectionFromBlock = [];
        /* This loop is to assign token object to presentDataMap array in tokenId as index.
         * To aviod includes check on array in below collectionResult's for loop
         */
        for (let i = 0; i < collectionOldInfo.length; i++) {
          presentCollectionMap[collectionOldInfo[i].collectionAddress] = collectionOldInfo[i].collectionAddress;
        }

        for (let i = 0; i < collectionBlockDatas.length; i++) {
          const collectionData: any = collectionBlockDatas[i].collectionData;
          //If already present in DB then update collection
          if (presentCollectionMap[collectionData.collectionAddress]) {
            void this.collectionModel.updateOne(
              { collectionAddress: collectionData.collectionAddress },
              {
                owner: collectionData.owner,
                royaltyRate: collectionData.royaltyRate,
                royaltyWallet: collectionData.royaltyWallet,
                collectionUpdatedAt: collectionData.collectionUpdatedAt,
              },
              {},
            );
          }
          //If not present in DB before
          else {
            void this.collectionModel.create(collectionData);
            presentCollectionMap[collectionData.collectionAddress] = collectionData.collectionAddress;
          }

          //Updates owner as artist
          if (collectionData?.owner) {
            void this.userModel.updateMany({ address: collectionData.owner }, { artistStatus: CommonStatus.ACTIVE }, {});
          }

          //Deletes the inserted collection from blockdata collection
          deleteCollectionFromBlock.push(collectionBlockDatas[i]._id);
        }
        await this.blockDataModel.deleteMany({ _id: { $in: deleteCollectionFromBlock } });
      }
      this.logger.info(`Updated collection data from blockdata to collections ${moment().format()}`);
    } catch (error) {
      this.logger.error(`Error while saving collection data in blockdata ${error?.message}`);
    }
  }

  /**
   *
   * This method updates token data from block data. It fetches token block data from the database,
   * processes it, and updates the token information in the token collection. It also handles the creation
   * of activity logs for token transfers, mints, and burns. Additionally, it updates user information and
   * collection statistics.
   *
   * @async
   * @method updateTokenDataFromBlockData
   * @returns {Promise<void>} - A promise that resolves when the token data has been updated.
   *
   * @throws {Error} - Throws an error if there is an issue while saving token data in block data.
   *
   * @example
   * ```typescript
   * await this.updateTokenDataFromBlockData();
   * ```
   *
   * @remarks
   * - This method is intended to be used as a cron job to periodically update token data.
   * - It processes up to 500 token block data entries at a time.
   * - The method ensures that token data is updated efficiently by using a map for O(1) access.
   * - It handles different types of activities such as transfers, mints, and burns based on the token data.
   * - The method also updates user information and collection statistics as needed.
   */
  @Cron("0 */3 * * * *")
  async updateTokenDataFromBlockData() {
    try {
      this.logger.info(`******************* Token insert cron started at ${moment().format()} ****************`);
      const completedBlock = (await this.cacheManager.get<number>("blockNo")) ?? null;

      if (!completedBlock) return;
      const tokenBlockDatas = await this.blockDataModel
        .find({ type: EntityType.TOKEN, blockNumber: { $lte: completedBlock } })
        .sort({ blockNumber: 1 })
        .limit(500)
        .lean();
      if (tokenBlockDatas.length > 0) {
        const deleteTokenFromBlock = [];

        const whereCond = { $or: [] };
        tokenBlockDatas.map(item => {
          whereCond.$or.push({ tokenId: item.tokenData.tokenId, collectionAddress: item.tokenData.collectionAddress });
        });

        const tokenOldData = await this.tokenModel.find(whereCond).select({ tokenId: 1, collectionAddress: 1, currentOwnerIds: 1 }).lean();
        const presentTokenDataMap = {};
        /** This loop is to assign token object to presentTokenDataMap array in tokenId +collectionAddress string as index. To access this object in O(1)*/
        for (let i = 0; i < tokenOldData.length; i++) {
          presentTokenDataMap[tokenOldData[i].tokenId + tokenOldData[i].collectionAddress] = tokenOldData[i];
        }

        const activityCreateData = [];
        const collectionAddressSet = new Set();
        for (let i = 0; i < tokenBlockDatas.length; i++) {
          const tokenInfo: any = tokenBlockDatas[i].tokenData;
          const presentData = presentTokenDataMap[tokenInfo.tokenId + tokenInfo.collectionAddress];

          //If already present in DB then update token
          if (presentData) {
            const activity = tokenInfo.transactionHash ? await this.activityModel.find({ hash: tokenInfo.transactionHash }) : [];
            // If the activity is already present in DB for the given hash
            if (activity.length < 1) {
              const fromAddress = tokenInfo?.fromUserId;
              const toAddress = tokenInfo?.currentOwnerIds[0]?.walletAddress;
              const zeroAddress = "******************************************";
              const nftQuantity = BigInt(tokenInfo?.currentOwnerIds[0]?.qty.toString());
              if (fromAddress !== zeroAddress && toAddress !== zeroAddress) {
                activityCreateData.push({
                  entityType: EntityType.TOKEN,
                  tokenId: tokenInfo?.tokenId,
                  collectionAddress: tokenInfo?.collectionAddress,
                  activityType: ActivityType.TRANSFER,
                  fromUser: tokenInfo?.fromUserId || null,
                  toUser: tokenInfo?.currentOwnerIds[0]?.walletAddress,
                  signature: null,
                  hash: tokenInfo?.transactionHash,
                  url: null,
                });
              } else if (fromAddress === zeroAddress) {
                activityCreateData.push({
                  entityType: EntityType.TOKEN,
                  tokenId: tokenInfo.tokenId,
                  collectionAddress: tokenInfo?.collectionAddress,
                  activityType: ActivityType.MINT,
                  fromUser: tokenInfo?.fromUserId || null,
                  toUser: tokenInfo.currentOwnerIds[0]?.walletAddress,
                  signature: null,
                  hash: tokenInfo.transactionHash,
                  url: null,
                });
              } else if (toAddress === zeroAddress) {
                activityCreateData.push({
                  entityType: EntityType.TOKEN,
                  tokenId: tokenInfo.tokenId,
                  collectionAddress: tokenInfo?.collectionAddress,
                  activityType: ActivityType.BURN,
                  fromUser: tokenInfo?.fromUserId || null,
                  toUser: tokenInfo.currentOwnerIds[0]?.walletAddress,
                  signature: null,
                  hash: tokenInfo.transactionHash,
                  url: null,
                });
              }

              // Assigns current ownerIds
              const currentUserIds = [];
              let toUserExist = false;
              presentData?.currentOwnerIds.forEach(user => {
                const userQty = BigInt(user.qty.toString());
                /** If toUser already has any quantity of token, quantity is increased */
                if (user.walletAddress === toAddress) {
                  currentUserIds.push({
                    walletAddress: user.walletAddress,
                    qty: new mongoose.Types.Decimal128((userQty + nftQuantity).toString()),
                  });
                  toUserExist = true;
                } else if (user.walletAddress === fromAddress) {
                  /**Control enter inside this condition if the tokenstandard is ERC721 */
                  /** If fromUser already has more quantity, quantity is descreased */
                  if (userQty > nftQuantity) {
                    currentUserIds.push({
                      walletAddress: user.walletAddress,
                      qty: new mongoose.Types.Decimal128((userQty - nftQuantity).toString()),
                    });
                  }
                } else {
                  /* Other than from and to users */
                  currentUserIds.push(user);
                }
              });
              /** If user does not exist in currentUserIds then new one is added */
              if (!toUserExist && toAddress !== zeroAddress) {
                currentUserIds.push({
                  walletAddress: toAddress,
                  qty: new mongoose.Types.Decimal128(nftQuantity.toString()),
                });
              }
              tokenInfo.currentOwnerIds = currentUserIds;

              if (currentUserIds.length) {
                //Updates tokeninfo in token collection
                await this.tokenModel.updateOne(
                  { collectionAddress: tokenInfo.collectionAddress, tokenId: tokenInfo.tokenId },
                  {
                    name: tokenInfo.name,
                    description: tokenInfo.description,
                    logo: tokenInfo.logo,
                    properties: tokenInfo.properties,
                    tokenId: tokenInfo.tokenId,
                    tokenStandard: tokenInfo.tokenStandard,
                    fromUserId: tokenInfo.fromUserId,
                    currentOwnerIds: tokenInfo.currentOwnerIds,
                    blockNo: tokenInfo.blockNo,
                    collectionAddress: tokenInfo.collectionAddress,
                    transactionHash: tokenInfo.transactionHash,
                    tokenCreatedAt: tokenInfo.tokenCreatedAt,
                    tokenUpdatedAt: tokenInfo.tokenUpdatedAt,
                    royaltyRate: tokenInfo.royaltyRate,
                    royaltyWallet: tokenInfo.royaltyWallet,
                  },
                  {},
                );
              } else {
                await this.tokenModel.deleteOne({ collectionAddress: tokenInfo.collectionAddress, tokenId: tokenInfo.tokenId });
              }
            }
          }

          //If token not present in DB then create token and mint activity
          else {
            // if (tokenInfo?.tokenId != "0") {
            await this.tokenModel.create(tokenInfo);

            activityCreateData.push({
              entityType: EntityType.TOKEN,
              tokenId: tokenInfo.tokenId,
              collectionAddress: tokenInfo?.collectionAddress,
              activityType: ActivityType.MINT,
              floorPrice: 0,
              fromUser: tokenInfo?.fromUserId || null,
              toUser: tokenInfo.currentOwnerIds[0]?.walletAddress,
              signature: null,
              hash: tokenInfo.transactionHash,
              url: null,
            });

            //Data is inserted in presentTokenDataMap array for O(1) access
            presentTokenDataMap[tokenInfo.tokenId + tokenInfo.collectionAddress] = {
              tokenId: tokenInfo.tokenId,
              collectionAddress: tokenInfo.collectionAddress,
              currentOwnerIds: tokenInfo.currentOwnerIds,
            };
            // }
          }
          if (tokenInfo?.originalOwnerId) {
            void this.userModel.updateOne({ address: tokenInfo.originalOwnerId }, { artistStatus: CommonStatus.ACTIVE }, {});
          }
          collectionAddressSet.add(tokenInfo.collectionAddress);

          //Deletes the inserted token from blockdata collection
          deleteTokenFromBlock.push(tokenBlockDatas[i]._id);
        }
        await Promise.all([this.activityModel.insertMany(activityCreateData), this.blockDataModel.deleteMany({ _id: { $in: deleteTokenFromBlock } })]);
        collectionAddressSet.forEach((address: string) => void this.statisticsUpdateService._updatePropertyStats(address));
        this.logger.info(`Updated token data from blockdata to tokens collections ${moment().format()}`);
      }
    } catch (error) {
      this.logger.error(`Error while saving token data in blockdata ${error?.message}`);
    }
  }

  /**
   * Automatically delists collections that have had no activity for the last 6 months.
   *
   * This method checks for collections that have not been updated in the past 6 months
   * and marks them as hidden and delisted. It updates the `isHidden` field to `true`
   * and the `status` field to `Status.DELISTED` for these collections.
   *
   * @returns {Promise<void>} A promise that resolves when the operation is complete.
   *
   * @throws Will log an error message if the operation fails.
   *
   * @example
   * ```typescript
   * await automaticallyDelistCollection();
   * ```
   */
  @Cron("0 0 0 */1 * *")
  async automaticallyDelistCollection() {
    //delist automatically collection when there is no activity for last 6 months
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const sixMonthBeforeDate = new Date(today.getTime() - 180 * 24 * 60 * 60 * 1000);
      await this.collectionModel.updateMany({ updatedAt: { $lte: sixMonthBeforeDate } }, { $set: { isHidden: true, status: Status.DELISTED } });
    } catch (error) {
      this.logger.error(`automaticallyDelistCollection ${error?.message}`, error);
    }
  }
}
