import { CollectionStatsCronService } from "./collection-stats-cron.service";
import { Activity, ActivitySchema } from "../../database/schemas/activity.schema";
import { Collections, CollectionsSchema } from "../../database/schemas/collections.schema";
import { StoreFront, StoreFrontSchema } from "../../database/schemas/store-front.schema";
import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";

@Module({
  providers: [CollectionStatsCronService],
  imports: [
    MongooseModule.forFeature([{ name: Collections.name, schema: CollectionsSchema }]),
    MongooseModule.forFeature([{ name: StoreFront.name, schema: StoreFrontSchema }]),
    MongooseModule.forFeature([{ name: Activity.name, schema: ActivitySchema }]),
  ],
})
export class CollectionStatsCronModule {}
