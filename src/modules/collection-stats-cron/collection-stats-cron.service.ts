import { ActivityType } from "../../database/enums/activity-type.enum";
import { Collections, CollectionsDocument } from "../../database/schemas/collections.schema";
import { Inject, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Cron } from "@nestjs/schedule";
import moment from "moment";
import mongoose, { Model } from "mongoose";
import { WINSTON_MODULE_PROVIDER } from "nest-winston";
import { Logger } from "winston";

@Injectable()
export class CollectionStatsCronService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    @InjectModel(Collections.name)
    private collectionModel: Model<CollectionsDocument>,
  ) {}

  /**
   * @description This method is a scheduled cron job that updates the statistics for each collection
   * in the database. It calculates the sales, average prices, and price changes over different time
   * periods (1 day, 7 days, and 30 days) based on the activity data. The results are then updated
   * in the collection's statistics.
   *
   * @cron "0 0 0 * /1 * *" - This cron expression schedules the job to run once a day at midnight.
   *
   * @async
   * @throws {InternalServerErrorException} Throws an internal server error if an unexpected error occurs.
   * @throws {Error} Re-throws any non-500 status code errors encountered during execution.
   *
   * @returns {Promise<void>} This method does not return a value.
   */
  @Cron("0 0 0 */1 * *")
  async updateStats() {
    try {
      this.logger.info(`******************* Collection-stats Cron started at ${moment().format()} ****************`);
      const today = new Date();
      today.setHours(0, 0, 0, 0); //getting today's start of day

      const sevenDaysBeforeDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000); //getting 7 days prior date
      const thirtyDaysBeforeDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000); //getting 30 days prior date

      const oneDaysBeforeDate = new Date(today.getTime() - 1 * 24 * 60 * 60 * 1000); //getting 1 days prior date
      const collectionActivityInfo = await this.collectionModel.aggregate([
        {
          $lookup: {
            from: "activities",
            foreignField: "collectionAddress",
            localField: "collectionAddress",
            pipeline: [
              {
                $match: {
                  activityType: ActivityType.SALE,
                }, //filtering the activities to get only SALE activity
              },
              {
                $sort: {
                  createdAt: 1,
                }, //sorting the activities in ascending order based on creation date
              },
            ],
            as: "activity",
          },
        },
      ]);

      for (let i = 0; i < collectionActivityInfo.length; i++) {
        const collectionInfo = collectionActivityInfo[i];
        let sevenDaySale = 0,
          sevenDayAvgPrice = 0,
          sevenDayChange = 0,
          sevenDayCount = 0,
          sevenDayFirstPrice = 0;

        const lastPrice = collectionInfo.activity.length > 0 ? collectionInfo.activity.at(-1) : 0; // Check for empty array

        let thirtyDaySale = 0,
          thirtyDayAvgPrice = 0,
          thirtyDayChange = 0,
          thirtyDayCount = 0,
          thirtyDayFirstPrice = 0;

        let oneDaySale = 0,
          oneDayAvgPrice = 0,
          oneDayChange = 0,
          oneDayCount = 0,
          oneDayFirstPrice = 0;

        let totalVolume = 0;

        let sevenDayFirstStatus = false,
          thirtyDayFirstStatus = false,
          oneDayFirstStatus = false;
        for (let j = 0; j < collectionInfo.activity.length; j++) {
          const activityInfo = collectionInfo.activity[j];
          const price = activityInfo.price / Math.pow(10, 18);

          totalVolume += Number(price) || 0;
          if (activityInfo.createdAt > sevenDaysBeforeDate) {
            if (!sevenDayFirstStatus) {
              sevenDayFirstPrice = price || 0;
              sevenDayFirstStatus = true;
            }
            sevenDayCount++;
            sevenDaySale += Number(price) || 0;
          }

          if (activityInfo.createdAt > thirtyDaysBeforeDate) {
            if (!thirtyDayFirstStatus) {
              thirtyDayFirstPrice = price || 0;
              thirtyDayFirstStatus = true;
            }
            thirtyDayCount++;
            thirtyDaySale += Number(price) || 0;
          }

          if (activityInfo.createdAt > oneDaysBeforeDate) {
            if (!oneDayFirstStatus) {
              oneDayFirstPrice = price || 0;
              oneDayFirstStatus = true;
            }
            oneDayCount++;
            oneDaySale += Number(price) || 0;
          }
        }
        sevenDayAvgPrice = sevenDayCount > 0 ? sevenDaySale / sevenDayCount : 0;
        sevenDayChange = this._getChangedPrice(sevenDayFirstPrice, lastPrice, sevenDayCount) || 0;
        thirtyDayAvgPrice = thirtyDayCount > 0 ? thirtyDaySale / thirtyDayCount : 0;
        thirtyDayChange = this._getChangedPrice(thirtyDayFirstPrice, lastPrice, thirtyDayCount) || 0;
        oneDayAvgPrice = oneDayCount > 0 ? oneDaySale / oneDayCount : 0;
        oneDayChange = this._getChangedPrice(oneDayFirstPrice, lastPrice, oneDayCount) || 0;
        const floorPriceChange = this._getChangedPrice(collectionInfo?.stats?.floorPriceOneDay, collectionInfo?.stats?.floorPrice, 0) || 0;
        await this.collectionModel.findByIdAndUpdate(collectionInfo._id, {
          $set: {
            "stats.floorPriceOneDay": collectionInfo?.stats?.floorPrice || 0,
            "stats.floorPriceOneDayChange": this._convertToString(floorPriceChange, 0) || 0,
            "stats.thirtyDaySale": this._convertToString(thirtyDaySale, 18),
            "stats.thirtyDayVolume": this._convertToString(thirtyDaySale, 18),
            "stats.thirtyDayAvgPrice": this._convertToString(thirtyDayAvgPrice, 18),
            "stats.thirtyDayChange": this._convertToString(thirtyDayChange, 18),
            "stats.sevenDaySale": this._convertToString(sevenDaySale, 18),
            "stats.sevenDayVolume": this._convertToString(sevenDaySale, 18),
            "stats.sevenDayAvgPrice": this._convertToString(sevenDayAvgPrice, 18),
            "stats.sevenDayChange": this._convertToString(sevenDayChange, 18),
            "stats.oneDaySale": this._convertToString(oneDaySale, 18),
            "stats.oneDayVolume": this._convertToString(oneDaySale, 18),
            "stats.oneDayAvgPrice": this._convertToString(oneDayAvgPrice, 18),
            "stats.oneDayChange": this._convertToString(oneDayChange, 18),
            totalVolume: this._convertToString(totalVolume, 18),
          },
        });
      }
      this.logger.info(`******************* Collection-stats Cron completed at ${moment().format()} ****************`);
    } catch (error) {
      this.logger.error(error?.message, error);
      if (error?.response?.statusCode !== 500) throw error;
      throw new InternalServerErrorException();
    }
  }

  /**
   * Calculates the percentage change between two prices.
   *
   * @param {number} [first=0] - The initial price value. Defaults to 0 if not provided.
   * @param {number} [last=0] - The final price value. Defaults to 0 if not provided.
   * @param {number} count - The number of transactions or data points considered.
   * @returns {number} The percentage change from the first price to the last price.
   *                   If the first price is zero, the change is considered zero to avoid division by zero.
   */
  _getChangedPrice(first = 0, last = 0, count) {
    const oneDayChange = count == 1 ? last : last - first;
    const changeRatio = first == 0 ? 0 : oneDayChange / first;
    const oneDayChangePercentage = Math.round(changeRatio * 10000) / 100;
    return oneDayChangePercentage;
  }

  /**
   * Converts a number to a string representation with a specified number of decimal places,
   * and returns it as a Decimal128 type.
   *
   * @param {number} number - The number to be converted.
   * @param {number} decimals - The number of decimal places to include in the string representation.
   * @returns {mongoose.Types.Decimal128} A Decimal128 representation of the number with the specified decimal places.
   */
  _convertToString(number, decimals) {
    return new mongoose.Types.Decimal128(
      Number(number * Math.pow(10, decimals)).toLocaleString("fullwide", {
        useGrouping: false,
      }),
    );
  }
}
