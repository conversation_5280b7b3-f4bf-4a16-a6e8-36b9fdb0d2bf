import { AuctionService } from "./auction.service";
import { Activity, ActivitySchema } from "../../database/schemas/activity.schema";
import { Auction, AuctionSchema } from "../../database/schemas/auction.schema";
import { Collections, CollectionsSchema } from "../../database/schemas/collections.schema";
import { Offers, OffersSchema } from "../../database/schemas/offer.schema";
import { Tokens, TokensSchema } from "../../database/schemas/tokens.schema";
import { Web3Instance } from "../../blockchain/web3Instance";
import { NotificationSetting, NotificationSettingSchema } from "../../database/schemas/notifications-setting.schema";
import { Notifications, NotificationsSchema } from "../../database/schemas/notifications.schema";
import { StatisticsUpdateService } from "../../shared/Providers/statistics-update.service";
import { MongooseModule } from "@nestjs/mongoose";
import { Module } from "@nestjs/common";

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Collections.name, schema: CollectionsSchema }]),
    MongooseModule.forFeature([{ name: Tokens.name, schema: TokensSchema }]),
    MongooseModule.forFeature([{ name: Auction.name, schema: AuctionSchema }]),
    MongooseModule.forFeature([{ name: Offers.name, schema: OffersSchema }]),
    MongooseModule.forFeature([{ name: Activity.name, schema: ActivitySchema }]),
    MongooseModule.forFeature([{ name: NotificationSetting.name, schema: NotificationSettingSchema }]),
    MongooseModule.forFeature([{ name: Notifications.name, schema: NotificationsSchema }]),
  ],
  providers: [AuctionService, Web3Instance, StatisticsUpdateService],
})
export class AuctionModule {}
