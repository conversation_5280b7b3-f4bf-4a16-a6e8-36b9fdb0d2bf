import { Auction, AuctionDocument } from "../../database/schemas/auction.schema";
import { AuctionType } from "../../shared/enums/auction-type.enum";
import { Offers, OffersDocument } from "../../database/schemas/offer.schema";
import { Tokens, TokensDocument } from "../../database/schemas/tokens.schema";
import { Web3Instance } from "../../blockchain/web3Instance";
import { pixelParkExchangeAbi } from "../..//blockchain/abi/pixelparkexchange.abi";
import { BlockChainConstant } from "../../blockchain/constants";
import { EntityType } from "../../shared/enums/entity-type.enum";
import { ActivityType } from "../../database/enums/activity-type.enum";
import { ActivitiesDocument, Activity } from "../../database/schemas/activity.schema";
import { NotificationSetting, NotificationSettingDocument } from "../../database/schemas/notifications-setting.schema";
import { Notifications, NotificationsDocument } from "../../database/schemas/notifications.schema";
import { Collections, CollectionsDocument } from "../../database/schemas/collections.schema";
import { NotificationsTypes } from "../../database/enums/notifications-types.enum";
import { StatisticsUpdateService } from "../../shared/Providers/statistics-update.service";
import { ConfigService } from "@nestjs/config";
import moment from "moment";
import { WINSTON_MODULE_PROVIDER } from "nest-winston";
import mongoose, { ClientSession, Model } from "mongoose";
import { Logger } from "winston";
import { Cron } from "@nestjs/schedule";
import { InjectConnection, InjectModel } from "@nestjs/mongoose";
import { Inject, Injectable, InternalServerErrorException } from "@nestjs/common";

@Injectable()
export class AuctionService {
  private CURRENT_TIME: any;
  private instance: any;
  environment: any;
  constructor(
    private web3Instance: Web3Instance,
    private configService: ConfigService,
    private statisticsUpdateService: StatisticsUpdateService,

    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    @InjectModel(Auction.name)
    private auctionModel: Model<AuctionDocument>,
    @InjectModel(Offers.name)
    private offerModel: Model<OffersDocument>,
    @InjectModel(Tokens.name)
    private tokenModel: Model<TokensDocument>,
    @InjectModel(Activity.name)
    private activityModel: Model<ActivitiesDocument>,
    @InjectModel(NotificationSetting.name)
    private notificationSettingModel: Model<NotificationSettingDocument>,
    @InjectModel(Notifications.name)
    private notificationModel: Model<NotificationsDocument>,
    @InjectModel(Collections.name)
    private collectionModel: Model<CollectionsDocument>,

    @InjectConnection() private readonly connection: mongoose.Connection,
  ) {
    this.instance = this.web3Instance.getWeb3Instance();
    this.environment = this.configService.get("NODE_ENV");
  }

  /**
   * @description
   * This method is a scheduled task that runs every 3 hours to update the declining price of active Dutch auctions.
   * It retrieves all active Dutch auctions from the database and calculates the current price based on the elapsed time
   * since the auction started. The updated price is then saved back to the database.
   *
   * @cron
   * The method is decorated with the `@Cron` decorator, which schedules it to run every 3 hours.
   *
   * @throws {InternalServerErrorException}
   * Throws an InternalServerErrorException if an error occurs during the process and the error's response status code is 500.
   *
   * @async
   * This method is asynchronous and uses the `async/await` pattern to handle asynchronous operations.
   *
   * @returns {Promise<void>}
   * This method does not return any value.
   */
  @Cron("0 */3 * * * *")
  async dutchAuctionPriceUpdate() {
    try {
      /*List of active auctions */
      const activeAuctions = await this.auctionModel.find({
        type: AuctionType.DUTCH_AUCTION,
        status: true, // Status true if auction is active
      });

      this.CURRENT_TIME = moment().unix();

      /**Loops through each active document and calculates declining price and updates it in DB */
      activeAuctions.forEach(auction => {
        void this._calculateAndUpdatePrice(auction); // This will be called asyncly since await is not used.
      });
      return;
    } catch (error) {
      this.logger.error(error?.message, error);
      if (error?.response?.statusCode !== 500) throw error;
      throw new InternalServerErrorException();
    }
  }

  /**
   * @description
   * Calculates the current declining price of a Dutch auction and updates it in the database.
   * This method is specifically designed for auctions where the price decreases over time
   * from a starting price to a reserved price.
   *
   * @param {Object} auction - The auction document containing details of the auction.
   * @param {number} auction.startTime - The Unix timestamp representing when the auction started.
   * @param {number} auction.endTime - The Unix timestamp representing when the auction ends.
   * @param {number} auction.startingPrice - The initial price of the auction in Wei.
   * @param {number} auction.reservedPrice - The minimum price of the auction in Wei.
   *
   * @returns {Promise<void>} - This method does not return any value.
   *
   * @throws {Error} - Throws an error if an unexpected issue occurs during the process.
   * If the error's response status code is not 500, it is rethrown.
   *
   * @async
   * This method is asynchronous and uses the `async/await` pattern to handle asynchronous operations.
   *
   * @example
   * // Assuming `auction` is a valid auction document
   * await _calculateAndUpdatePrice(auction);
   *
   * @remarks
   * - The method first checks if the current time has surpassed the auction's end time.
   *   If so, it deactivates the auction and exits.
   * - If the auction is still active, it calculates the current price using a linear
   *   interpolation formula based on the elapsed time since the auction started.
   * - The calculated price is then updated in the auction document and saved to the database.
   * - The method includes error handling to log and manage any issues that arise during execution.
   */
  async _calculateAndUpdatePrice(auction) {
    try {
      // eslint-disable-next-line prefer-const
      let { startTime, endTime, startingPrice, reservedPrice } = auction;
      startingPrice = startingPrice / Math.pow(10, 18);
      reservedPrice = reservedPrice / Math.pow(10, 18);

      /** If current time is greater than end time */
      if (this.CURRENT_TIME >= endTime) {
        void this._disableAuction(auction); // Auction deactivated
        //cancelList activity

        return;
      }
      /** Diff between auction start and end date in seconds */
      const endStartDiff = endTime - startTime;

      /** Diff between current and start date in seconds */
      const currentStartDiff = this.CURRENT_TIME - startTime;
      /** Formula to calculate the decline price */
      const calculatedPrice = startingPrice - ((startingPrice - (reservedPrice || 0)) * currentStartDiff) / endStartDiff;

      auction.currentPrice = calculatedPrice * Math.pow(10, 18);
      auction.save();
    } catch (error) {
      this.logger.error(error?.message, error);
      if (error?.response?.statusCode !== 500) throw error;
      return;
    }
  }

  /**
   * @description
   * This method is a scheduled task that runs every 3 hours to update the status of active English auctions.
   * It retrieves all active English auctions that have ended and processes them to determine the highest bidder.
   * If the highest bid meets the required threshold, the auction is settled by transferring the item to the highest bidder.
   * The method also disables any offers that are no longer valid.
   *
   * @cron
   * The method is decorated with the `@Cron` decorator, which schedules it to run every 3 hours.
   *
   * @throws {InternalServerErrorException}
   * Throws an InternalServerErrorException if an error occurs during the process and the error's response status code is 500.
   *
   * @async
   * This method is asynchronous and uses the `async/await` pattern to handle asynchronous operations.
   *
   * @returns {Promise<void>}
   * This method does not return any value.
   *
   * @remarks
   * - The method first retrieves all active English auctions that have ended.
   * - It adds the admin wallet to the Ethereum accounts to incur the cost of the auction transfer.
   * - A contract instance for the PixelParkExchange is created to interact with the blockchain.
   * - For each auction, the `_updateHighestBidder` method is called to find and update the highest bidder.
   * - The `_disableOffer` method is called to deactivate any offers that are no longer valid.
   * - The method includes error handling to log and manage any issues that arise during execution.
   */
  @Cron("0 */3 * * * *")
  async englishAuctionOwnerUpdate() {
    try {
      /*List of active auctions */
      const activeAuctions = await this.auctionModel.find({
        type: AuctionType.ENGLISH_AUCTION,
        status: true, // Status true if auction is active
        endTime: {
          $lte: moment().unix(),
        },
      });

      //Adding admin wallet to incur the cost of the transfer of auction
      this.instance.eth.accounts.wallet.add(this.configService.get("ADMIN_WALLET_PRIVATE"));
      //Creating the contractinstance for pixelparkexchange
      const contractInstance = new this.instance.eth.Contract(pixelParkExchangeAbi, this.configService.get("PIXELPARK_EXCHANGE_ADDRESS"));
      /**Loops through each active document and find the highest bidder and updates it in DB */
      activeAuctions.forEach(auction => {
        void this._updateHighestBidder(auction, contractInstance); // This will be called asyncly since await is not used.
      });
      void this._disableOffer();

      return;
    } catch (error) {
      this.logger.error(error?.message, error);
      if (error?.response?.statusCode !== 500) throw error;
      throw new InternalServerErrorException();
    }
  }

  /**
   * @description
   * This method identifies the highest bidder for a given auction and processes the transaction to transfer the auctioned item to the highest bidder.
   * It performs several key operations:
   * - Finds the highest active offer for the auction.
   * - Checks if the highest offer meets the minimum threshold to settle the auction.
   * - Constructs the necessary data structures for the blockchain transaction.
   * - Estimates the gas required for the transaction and executes it.
   * - Updates the database to reflect the new owner of the auctioned item.
   * - Records the sale and transfer activities in the activity log.
   * - Sends notifications to relevant users about the auction outcome.
   * - Deactivates all other offers related to the auction.
   * - Updates the auction status to indicate it is complete.
   * - Updates collection statistics post-auction.
   *
   * @param {Object} auction - The auction document containing details of the auction.
   * @param {Object} contractInstance - The blockchain contract instance used to interact with the auction contract.
   *
   * @returns {Promise<void>} - This method does not return any value.
   *
   * @throws {Error} - Throws an error if an unexpected issue occurs during the process.
   * If the error's response status code is not 500, it is rethrown.
   *
   * @async
   * This method is asynchronous and uses the `async/await` pattern to handle asynchronous operations.
   *
   * @example
   * // Assuming `auction` is a valid auction document and `contractInstance` is a valid contract instance
   * await _updateHighestBidder(auction, contractInstance);
   *
   * @remarks
   * - The method starts a database transaction to ensure atomicity of operations.
   * - It uses the `matchMakerBidWithMakerAsk` method of the contract to settle the auction on the blockchain.
   * - The method includes error handling to log and manage any issues that arise during execution.
   * - Notifications are sent to the auction owner, the highest bidder, and other bidders.
   * - The auction status is updated to inactive once the transaction is complete.
   */
  async _updateHighestBidder(auction, contractInstance) {
    const session = await this.connection.startSession();
    session.startTransaction();
    try {
      // Finding the highest bidder for the auction
      const highestOffer = await this.offerModel
        .find({
          auctionId: auction._id,
          active: true,
        })
        .sort({ price: -1 });
      if (
        highestOffer.length &&
        Number(highestOffer[0].price) >= BlockChainConstant.OFFER_PRICE_THRESHOLD //cbecking if the bid is greater than threshold set to settle the auction
      ) {
        const askSignature = auction.signature.substring(2);
        const bidSignature = highestOffer[0].signature.substring(2);
        //Creating the maker ask object for the matchMakerBidWithMakerAsk function
        const makerAsk = {
          isOrderAsk: auction.isOrderAsk,
          signer: auction.signer,
          collection: auction.collectionAddress,
          price: auction.price.toString(),
          tokenId: auction.tokenId,
          amount: auction.amount,
          strategy: auction.strategy,
          currency: auction.currency,
          nonce: auction.nonce,
          startTime: auction.startTime,
          endTime: auction.endTime,
          minPercentageToAsk: auction.minPercentageToAsk,
          params: auction.params[0],
          r: "0x" + askSignature.substring(0, 64),
          s: "0x" + askSignature.substring(64, 128),
          v: parseInt(askSignature.substring(128, 130), 16),
        };

        //Creating the maker bid object for the matchMakerBidWithMakerAsk function
        const makerBid = {
          isOrderAsk: highestOffer[0].isOrderAsk,
          signer: highestOffer[0].signer,
          collection: highestOffer[0].collectionAddress,
          price: highestOffer[0].price.toString(),
          tokenId: highestOffer[0].tokenId,
          amount: highestOffer[0].amount,
          strategy: highestOffer[0].strategy,
          currency: highestOffer[0].currency,
          nonce: highestOffer[0].nonce,
          startTime: highestOffer[0].startTime,
          endTime: highestOffer[0].endTime,
          minPercentageToAsk: highestOffer[0].minPercentageToAsk,
          params: highestOffer[0].params[0],
          r: "0x" + bidSignature.substring(0, 64),
          s: "0x" + bidSignature.substring(64, 128),
          v: parseInt(bidSignature.substring(128, 130), 16),
        };

        //Estimating the gas fee for the trasaction to settle the auction
        const estimatedGas = await contractInstance.methods.matchMakerBidWithMakerAsk(makerAsk, makerBid).estimateGas({ from: this.configService.get("ADMIN_WALLET_PUBLIC") });

        // function to move the token from auctioner to highest bidder
        await contractInstance.methods.matchMakerBidWithMakerAsk(makerAsk, makerBid).send({
          from: this.configService.get("ADMIN_WALLET_PUBLIC"),
          gas: estimatedGas,
        });

        //updating the highest bidder in the db
        void this._updateTokenUserId(auction, auction.signer, highestOffer[0].signer, session);
        const collectionInfo = await this.collectionModel.findOne({ collectionAddress: auction.collectionAddress }).lean();

        //creating activity for SALE and TRANSFER
        const activityData = {
          entityType: EntityType.TOKEN,
          tokenId: auction.tokenId,
          collectionAddress: auction?.collectionAddress,
          price: auction.reservedPrice,
          activityType: ActivityType.SALE,
          floorPrice: Number(auction.reservedPrice) <= Number(collectionInfo?.stats?.floorPrice) ? auction.reservedPrice : collectionInfo?.stats?.floorPrice,
          fromUser: auction.signer,
          toUser: highestOffer[0].signer,
          signature: auction.signature,
          hash: null,
          url: null,
        };

        /** Inserts two records in activity collection (Sale and transfer) */
        const activity = await this.activityModel.insertMany([
          activityData, // Sale activity record
          { ...activityData, activityType: ActivityType.TRANSFER }, // Transfer activity record
        ]);

        //sale and transfer for highest bidder, sale for all bidders and owner

        const notificationPromiseArray = [];
        const auctionWinner = highestOffer.shift();
        highestOffer.forEach(offer => {
          notificationPromiseArray.push(this.notificationActivity(offer.signer, activity[0]._id, NotificationsTypes.ITEM_SOLD));
        });

        notificationPromiseArray.push(
          this.notificationActivity(auction.signer, activity[0]._id, NotificationsTypes.ITEM_SOLD),
          this.notificationActivity(auctionWinner.signer, activity[1]._id, NotificationsTypes.SUCCESSFULL_PURCHASE),
        );
        await Promise.all(notificationPromiseArray);

        //clearing all the other offer in the auction once the auction is settled
        await this.offerModel.updateMany({ auctionId: auction._id, signer: auctionWinner.signer }, { $set: { active: false } }, { session });
        /** Updates auction status indicating it is over */
        auction.status = false;
        await auction.save({ session });
      }
      void this._disableAuction(auction);
      void this._updateCollectionStats(auction.collectionAddress);
      await session.commitTransaction();
      await session.endSession();
    } catch (error) {
      await session.abortTransaction();
      await session.endSession();
      this.logger.error(error?.message, error);
      if (error?.response?.statusCode !== 500) throw error;
      return;
    }
  }

  async _disableOffer() {
    await this.offerModel.updateMany(
      {
        endTime: {
          $lte: moment().unix(),
        },
      },
      { $set: { active: false } },
    );
  }

  /**
   * @description
   * Updates the ownership details of a token in the database after an auction transaction.
   * This method adjusts the quantities of the token held by the previous and new owners
   * and updates the token's status and offer details in the database.
   *
   * @param {Auction} auctionInfo - The auction document containing details of the auction.
   * @param {string} fromUser - The wallet address of the user who previously owned the token.
   * @param {string} toUser - The wallet address of the user who won the auction and is acquiring the token.
   * @param {ClientSession} session - The MongoDB client session used for transaction management.
   *
   * @returns {Promise<void>} - This method does not return any value.
   *
   * @throws {InternalServerErrorException} - Throws an InternalServerErrorException if an error occurs during the process.
   * If the error's response status code is not 500, it is rethrown.
   *
   * @async
   * This method is asynchronous and uses the `async/await` pattern to handle asynchronous operations.
   *
   * @example
   * // Assuming `auctionInfo` is a valid auction document, `fromUser` and `toUser` are valid wallet addresses,
   * // and `session` is a valid MongoDB client session
   * await _updateTokenUserId(auctionInfo, fromUser, toUser, session);
   *
   * @remarks
   * - The method first retrieves the current ownership details of the token from the database.
   * - It iterates over the current owners to adjust the quantities based on the auction transaction.
   * - If the new owner does not already exist in the ownership list, they are added with the acquired quantity.
   * - The method then updates the token's status, including its availability for purchase and any active offers.
   * - The method includes error handling to log and manage any issues that arise during execution.
   */
  async _updateTokenUserId(auctionInfo: Auction, fromUser: string, toUser: string, session: ClientSession) {
    try {
      const tokenInfo = await this.tokenModel
        .findOne({
          collectionAddress: auctionInfo.collectionAddress,
          tokenId: auctionInfo.tokenId,
        })
        .lean();

      const currentUserIds = [];
      let toUserExist = false;

      tokenInfo.currentOwnerIds.forEach(user => {
        /** If toUser already has any quantity of token, quantity is increased */
        if (user.walletAddress === toUser) {
          currentUserIds.push({
            walletAddress: user.walletAddress,
            qty: user.qty + auctionInfo.quantity,
          });
          toUserExist = true;
        } else if (user.walletAddress === fromUser) {
          /** If fromUser already has more quantity, quantity is descreased */
          if (user.qty > auctionInfo.quantity) {
            currentUserIds.push({
              walletAddress: user.walletAddress,
              qty: user.qty - auctionInfo.quantity,
            });
          }
        } else {
          /* Other than from and to users */
          currentUserIds.push(user);
        }
      });

      /** If user does not exist in currentUserIds then new one is added */
      if (!toUserExist) {
        currentUserIds.push({
          walletAddress: toUser,
          qty: auctionInfo.quantity,
        });
      }
      const offers = await this.offerModel.find({ tokenId: auctionInfo.tokenId, collectionAddress: auctionInfo.collectionAddress, active: true }).sort({ price: -1 });
      const highestOfferValue = offers.length > 0 ? offers[0]?.price : new mongoose.Types.Decimal128("0");

      await this.tokenModel.findOneAndUpdate(
        {
          tokenId: auctionInfo.tokenId,
          collectionAddress: auctionInfo.collectionAddress,
        },
        {
          buyNow: false,
          currentOwnerIds: currentUserIds,
          listedAt: null,
          price: 0,
          hasOffer: offers.length > 0,
          topOffer: highestOfferValue,
          auctionId: null,
          auctionType: null,
          auctionEndTime: null,
          reserveMet: false,
        },
        { session },
      );
    } catch (error) {
      this.logger.error(error?.message, error);
      if (error?.response?.statusCode !== 500) throw error;
      throw new InternalServerErrorException();
    }
  }

  /**
   * @description
   * Triggers a notification based on the specified activity type and user settings.
   * This method checks the user's notification settings and sends a notification
   * if the conditions for the specified activity type are met.
   *
   * @param {string} address - The wallet address of the user to notify.
   * @param {string} activityId - The ID of the activity that triggered the notification.
   * @param {NotificationsTypes} type - The type of notification to send.
   *
   * @returns {Promise<void>} - This method does not return any value.
   *
   * @throws {Error} - Logs an error if an unexpected issue occurs during the process.
   *
   * @async
   * This method is asynchronous and uses the `async/await` pattern to handle asynchronous operations.
   *
   * @example
   * // Assuming `address` is a valid wallet address, `activityId` is a valid activity ID,
   * // and `type` is a valid notification type
   * await notificationActivity(address, activityId, NotificationsTypes.ITEM_SOLD);
   *
   * @remarks
   * - The method first retrieves the user's notification settings and the activity details.
   * - It calculates a threshold based on the user's settings to determine if a bid activity
   *   notification should be sent.
   * - Depending on the notification type, it checks if the user has enabled notifications
   *   for that type and if any additional conditions (like price thresholds) are met.
   * - If the conditions are satisfied, it calls `saveNotification` to store the notification.
   * - The method includes error handling to log any issues that arise during execution.
   */
  async notificationActivity(address: string, activityId: string, type: NotificationsTypes) {
    try {
      const notificationPayload = {
        address,
        activityId,
        type,
      };
      const [enabledNotifications, activityInfo] = await Promise.all([
        this.notificationSettingModel
          .findOne({
            address,
          })
          .lean(),
        this.activityModel
          .findOne({
            _id: activityId,
          })
          .lean(),
      ]);

      const thresholdBid = enabledNotifications?.thresholdBid || 0;
      const threshold = thresholdBid * Math.pow(10, 18);

      // Item sold: when some one purchased the item (Notify owner and other users who has placed offers on item)
      if (type == NotificationsTypes.ITEM_SOLD && enabledNotifications.itemSold) {
        void this.saveNotification(notificationPayload);
      }
      // successfull purchased: when u successfully purchase the item(To the user who has purchased)
      else if (type == NotificationsTypes.SUCCESSFULL_PURCHASE && enabledNotifications.purchaseSuccess) {
        void this.saveNotification(notificationPayload);
      }
      // Owned Item: when updates occur on one of the item purchased (when some one makes offer on my item)
      else if (type == NotificationsTypes.OWNED_ITEM && enabledNotifications.ownedItem) {
        void this.saveNotification(notificationPayload);
      }
      // Bid Activity: when someone bids on your item (For english auction)
      else if (type == NotificationsTypes.BID_ACTIVITY && enabledNotifications.bidActivity && activityInfo.price >= threshold) {
        void this.saveNotification(notificationPayload);
      }
      // Price change: when price of your item changes
      else if (type == NotificationsTypes.PRICE_CHANGE && enabledNotifications.priceChange) {
        void this.saveNotification(notificationPayload);
      }
      //  Auction Ends: when auction ends (For english auction. For duction(notification will be sent in item sld or purchsae successfully))
      else if (type == NotificationsTypes.AUCTION_ENDS && enabledNotifications.auctionEnd) {
        void this.saveNotification(notificationPayload);
      }
      // outbid: when offer places by you is exceeded by another user (For english auction (basically to all users involved in item with offers))
      else if (type == NotificationsTypes.OUT_BID && enabledNotifications.outBid) {
        void this.saveNotification(notificationPayload);
      }
      // Newsletter: updates from pixel park team
      else if (type == NotificationsTypes.NEWS_LETTER && enabledNotifications.newsLetter) {
        void this.saveNotification(notificationPayload);
      }
    } catch (error) {
      this.logger.error(error?.message, error);
    }
  }

  async saveNotification(notifyPayload) {
    try {
      const notifications = this.notificationModel.create(notifyPayload);
      return notifications;
    } catch (error) {
      this.logger.error(error?.message, error);
      if (error?.response?.statusCode !== 500) throw error;
      throw new InternalServerErrorException();
    }
  }

  /**
   * @description
   * Updates the statistical data of a collection in the database. This method calculates
   * the total volume of sales, the sales volume for the last day, and the percentage change
   * in sales price over the last day for a given collection. The calculated statistics are
   * then updated in the collection's document in the database.
   *
   * @param {string} collectionAddress - The blockchain address of the collection whose statistics are to be updated.
   *
   * @returns {Promise<void>} - This method does not return any value.
   *
   * @throws {Error} - Throws an error if an unexpected issue occurs during the process.
   * If the error's response status code is not 500, it is rethrown.
   *
   * @async
   * This method is asynchronous and uses the `async/await` pattern to handle asynchronous operations.
   *
   * @example
   * // Assuming `collectionAddress` is a valid blockchain address of a collection
   * await _updateCollectionStats(collectionAddress);
   *
   * @remarks
   * - The method first calculates the sales volume and price change over the last day by
   *   aggregating activity data from the database.
   * - It then calculates the total sales volume for the collection by aggregating all
   *   transfer activities.
   * - The calculated statistics are updated in the collection's document in the database.
   * - The method includes error handling to log and manage any issues that arise during execution.
   */
  async _updateCollectionStats(collectionAddress: string) {
    const today = new Date();
    const oneDayBeforeDate = new Date(today.getTime() - 1 * 24 * 60 * 60 * 1000); //getting 30 days prior date

    const collectionActivityoneDays = await this.activityModel.aggregate([
      {
        $match: {
          collectionAddress,
          activityType: ActivityType.TRANSFER,
          createdAt: { $gte: oneDayBeforeDate },
        },
      },
      {
        $sort: {
          createdAt: 1,
        }, //sorting the activities in ascending order based on creation date
      },

      {
        $group: {
          _id: "$collectionAddress",
          first: { $first: "$$ROOT" },
          last: { $last: "$$ROOT" },
          oneDaySale: { $sum: "$price" },
        },
      },
    ]);
    let oneDayChangePercentage = 0,
      oneDayVolume = 0,
      oneDayChange = 0;
    if (collectionActivityoneDays.length) {
      oneDayVolume = collectionActivityoneDays[0]?.oneDaySale;
      oneDayChange = collectionActivityoneDays[0]?.last?.price - collectionActivityoneDays[0]?.first?.price;
      oneDayChangePercentage = Math.round((oneDayChange / collectionActivityoneDays[0]?.last?.price) * 100);
    }
    const collectionActivityTotalVolume = await this.activityModel.aggregate([
      { $match: { collectionAddress, activityType: ActivityType.TRANSFER } },
      {
        $sort: {
          createdAt: 1,
        }, //sorting the activities in ascending order based on creation date
      },
      {
        $group: {
          _id: "$collectionAddress",
          totalVolume: { $sum: "$price" },
        },
      },
    ]);
    await this.collectionModel.updateMany(
      { collectionAddress },
      {
        $set: {
          "stats.totalVolume": collectionActivityTotalVolume[0].totalVolume,
          "stats.oneDaySale": oneDayVolume,
          "stats.oneDayChange": new mongoose.Types.Decimal128(oneDayChangePercentage.toString()),
          "stats.oneDayVolume": oneDayVolume,
        },
      },
    );
  }

  /**
   * @description
   * This method is a scheduled task that runs every 3 hours to check for expired timed sales and silent auctions.
   * It retrieves all active auctions of type TIMED_SALE and SILENT_AUCTION that have reached their end time.
   * The method then deactivates these auctions by calling the `_disableAuction` method.
   *
   * @cron
   * The method is decorated with the `@Cron` decorator, which schedules it to run every 3 hours.
   *
   * @throws {InternalServerErrorException}
   * Throws an InternalServerErrorException if an error occurs during the process and the error's response status code is 500.
   *
   * @async
   * This method is asynchronous and uses the `async/await` pattern to handle asynchronous operations.
   *
   * @returns {Promise<void>}
   * This method does not return any value.
   *
   * @remarks
   * - The method first retrieves all active auctions of type TIMED_SALE and SILENT_AUCTION that have ended.
   * - It iterates over each auction and calls the `_disableAuction` method to deactivate it.
   * - The method includes error handling to log and manage any issues that arise during execution.
   */
  @Cron("0 */3 * * * *")
  async TimedSaleExpirationCheck() {
    try {
      /*List of active auctions */
      const activeAuctions = await this.auctionModel.find({
        type: { $in: [AuctionType.TIMED_SALE, AuctionType.SILENT_AUCTION] },
        status: true, // Status true if auction is active
        endTime: {
          $lte: moment().unix(),
        },
      });
      /**Loops through each active document and find the disable auction and updates it in DB */
      activeAuctions.forEach(auction => {
        void this._disableAuction(auction); // This will be called asyncly since await is not used.
      });
      return;
    } catch (error) {
      this.logger.error(error?.message, error);
      if (error?.response?.statusCode !== 500) throw error;
      throw new InternalServerErrorException();
    }
  }

  /**
   * @description
   * Deactivates an auction and updates related records in the database. This method is responsible for
   * marking an auction as inactive, creating a cancellation activity record, notifying the auction owner
   * of the auction's end, and updating the token's status and offers in the database.
   *
   * @param {Auction} auction - The auction document to be deactivated. This object contains details
   * about the auction, such as its status, token ID, collection address, and reserved price.
   *
   * @returns {Promise<void>} - This method does not return any value.
   *
   * @throws {Error} - Throws an error if an unexpected issue occurs during the process.
   * If the error's response status code is not 500, it is rethrown.
   *
   * @async
   * This method is asynchronous and uses the `async/await` pattern to handle asynchronous operations.
   *
   * @example
   * // Assuming `auction` is a valid auction document
   * await _disableAuction(auction);
   *
   * @remarks
   * - The method first sets the auction's status to false and saves the change to the database.
   * - It retrieves the collection information to determine the floor price for the activity record.
   * - An activity record of type CANCELLIST is created to log the auction's cancellation.
   * - A notification is sent to the auction owner to inform them that the auction has ended.
   * - The method updates the token's status in the database, including its availability for purchase,
   *   any active offers, and the highest offer value.
   * - The method calls the statistics update service to refresh the floor price of the collection.
   */
  async _disableAuction(auction) {
    auction.status = false;
    auction.save();
    const collectionInfo = await this.collectionModel.findOne({ collectionAddress: auction?.collectionAddress }).lean();
    const activityData = {
      entityType: EntityType.TOKEN,
      tokenId: auction?.tokenId,
      collectionAddress: auction?.collectionAddress,
      price: auction?.reservedPrice,
      floorPrice: Number(auction?.reservedPrice) <= Number(collectionInfo?.stats?.floorPrice) ? auction?.reservedPrice : collectionInfo?.stats?.floorPrice,
      activityType: ActivityType.CANCELLIST,
      fromUser: auction?.signer,
      toUser: null,
      signature: auction?.signature,
      hash: null,
      url: null,
    };
    const activity = await this.activityModel.create(
      activityData, // CANCELLIST activity record
    );
    await this.notificationActivity(auction.signer, activity._id, NotificationsTypes.AUCTION_ENDS);
    const offers = await this.offerModel.find({ tokenId: auction?.tokenId, collectionAddress: auction?.collectionAddress, active: true }).sort({ price: -1 });
    const highestOfferValue = offers.length > 0 ? offers[0]?.price : new mongoose.Types.Decimal128("0");
    await this.tokenModel.findOneAndUpdate(
      {
        collectionAddress: auction.collectionAddress,
        tokenId: auction.tokenId,
      },
      {
        buyNow: false,
        price: 0,
        listedAt: null,
        hasOffer: offers.length > 0,
        topOffer: highestOfferValue,
        auctionId: null,
        auctionType: null,
        auctionEndTime: null,
        reserveMet: false,
      },
      {},
    );
    void this.statisticsUpdateService._updateFloorPrice(auction.collectionAddress);
  }
}
