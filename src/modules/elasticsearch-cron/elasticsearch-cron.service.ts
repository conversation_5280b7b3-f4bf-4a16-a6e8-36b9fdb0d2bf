import { Collections, CollectionsDocument } from "../../database/schemas/collections.schema";
import { Tokens, TokensDocument } from "../../database/schemas/tokens.schema";
import { Users, UsersDocument } from "../../database/schemas/users.schema";
import { ElasticsearchService } from "../elasticsearch/elasticsearch.service";
import { CACHE_MANAGER, Inject, Injectable, OnModuleInit } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { WINSTON_MODULE_PROVIDER } from "nest-winston";
import { Cache } from "cache-manager";
import { Logger } from "winston";
import { Cron } from "@nestjs/schedule";
import { ConfigService } from "@nestjs/config";
import moment from "moment";
import * as fs from "fs";
import * as path from "path";

@Injectable()
export class ElasticsearchCronService implements OnModuleInit {
  environment: string;
  mappings: any;

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    @InjectModel(Collections.name)
    private collectionModel: Model<CollectionsDocument>,
    @InjectModel(Tokens.name)
    private tokenModel: Model<TokensDocument>,
    @InjectModel(Users.name)
    private userModel: Model<UsersDocument>,
    private readonly elasticsearchService: ElasticsearchService,
    private configService: ConfigService,
  ) {
    this.environment = this.configService.get("NODE_ENV");
    this.loadMappings();
  }

  async onModuleInit() {
    await this.initializeIndices();
  }

  /**
   * Load Elasticsearch mappings from the JSON file
   */
  private loadMappings() {
    try {
      const mappingsPath = path.join(process.cwd(), "src/modules/elasticsearch/mappings/elasticsearch-mappings.json");
      const mappingsData = fs.readFileSync(mappingsPath, "utf8");
      this.mappings = JSON.parse(mappingsData);
      this.logger.info("Elasticsearch mappings loaded successfully");
    } catch (error) {
      this.logger.error(`Failed to load Elasticsearch mappings: ${error.message}`);
    }
  }

  /**
   * Initialize Elasticsearch indices if they don't exist
   */
  async initializeIndices() {
    try {
      this.logger.info(`******** Initializing Elasticsearch indices at ${moment().format()} *******`);

      const indices = [`${this.environment}_users`, `${this.environment}_collections`, `${this.environment}_tokens`];

      for (const index of indices) {
        const exists = await this.elasticsearchService.indexExists(index);

        if (!exists) {
          // let mappingKey = "_user_token_collection";
          let mappingKey = "";
          if (index.includes("_users")) {
            mappingKey = "users";
          } else if (index.includes("_collections")) {
            mappingKey = "collections";
          } else if (index.includes("_tokens")) {
            mappingKey = "tokens";
          }

          const result = await this.elasticsearchService.createIndex(
            index,
            this.mappings.settings,
            mappingKey ? { properties: this.mappings.mappings[mappingKey].properties } : undefined,
          );

          this.logger.info(`Created index ${index}: ${JSON.stringify(result)}`);
        } else {
          this.logger.info(`Index ${index} already exists`);
        }
      }
    } catch (error) {
      this.logger.error(`Error initializing indices: ${error.message}`);
    }
  }

  /**
   * Index new documents in Elasticsearch
   */
  @Cron("0 */1 * * * *") // Run every 5 minutes
  async indexNewDocuments() {
    try {
      this.logger.info(`******** Cron to add new records in Elasticsearch started at ${moment().format()} *******`);
      const unindexedData = await Promise.all([
        this.getDataToBeIndexed("userModel", `${this.environment}_users`),
        this.getDataToBeIndexed("collectionModel", `${this.environment}_collections`),
        this.getDataToBeIndexed("tokenModel", `${this.environment}_tokens`),
      ]);
      await this.bulkIndexToElasticsearch(unindexedData);
    } catch (error) {
      this.logger.error(error.message);
    }
  }

  /**
   * Update existing documents in Elasticsearch
   */
  @Cron("0 */1 * * * *") // Run every 5 minutes
  async updateDocuments() {
    try {
      this.logger.info(`******** Cron to update data in Elasticsearch started at ${moment().format()} *******`);
      // const dataToUpdate = await Promise.all([
      //   this.findDataToBeUpdatedInElasticsearch("collectionModel", `${this.environment}_collections`),
      //   this.findDataToBeUpdatedInElasticsearch("tokenModel", `${this.environment}_tokens`),
      //   this.findDataToBeUpdatedInElasticsearch("userModel", `${this.environment}_users`),
      // ]);

      const dataToUpdate = [];

      // Process collections first (smaller datasets)
      const collectionData = await this.findDataToBeUpdatedInElasticsearch("collectionModel", `${this.environment}_collections`);
      dataToUpdate.push(collectionData);

      const userData = await this.findDataToBeUpdatedInElasticsearch("userModel", `${this.environment}_users`);
      dataToUpdate.push(userData);

      // Process tokens last
      const tokenData = await this.findDataToBeUpdatedInElasticsearch("tokenModel", `${this.environment}_tokens`);
      dataToUpdate.push(tokenData);

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      await this.bulkIndexToElasticsearch(dataToUpdate);
    } catch (error) {
      this.logger.error(error.message);
    }
  }

  /**
   * Get data to be indexed in Elasticsearch
   */
  async getDataToBeIndexed(model: string, indexName: string) {
    try {
      const elasticsearchBatchData = [];
      let selectData = {};
      let populateOptions = null;

      if (model === "userModel") {
        selectData = { _id: 1, name: 1, address: 1, isVerified: 1, avatar: 1, status: 1 };
      } else if (model === "collectionModel") {
        selectData = { _id: 1, name: 1, slug: 1, logo: 1, stats: 1, totalSupply: 1, collectionAddress: 1, isVerified: 1, status: 1 };
      } else if (model === "tokenModel") {
        selectData = { _id: 1, name: 1, logo: 1, buyNow: 1, collectionAddress: 1, tokenId: 1, description: 1, status: 1, isCollectionBlacklisted: 1 };
        // Only populate for tokens since they need collection info
        populateOptions = { path: "collectionInfo", strictPopulate: false, select: ["_id", "name", "collectionAddress", "isVerified"] };
      }

      let query = this[model].find({ elasticsearchId: null });

      if (populateOptions) {
        query = query.populate(populateOptions);
      }

      const unindexedData = await query.select(selectData).skip(0).limit(5000).lean();

      for (let i = 0; i < unindexedData.length; i++) {
        elasticsearchBatchData.push({
          index: {
            _index: indexName,
            _id: unindexedData[i]._id.toString(),
          },
        });

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { _id: id, ...documentWithoutId } = unindexedData[i];

        // Transform data based on model type to match mappings
        let transformedDocument = documentWithoutId;

        if (model === "collectionModel") {
          // Transform collection data to match mapping
          // Convert Decimal128 fields to numbers
          const totalSupply = documentWithoutId.totalSupply && documentWithoutId.totalSupply.$numberDecimal ? parseFloat(documentWithoutId.totalSupply.$numberDecimal) : 0;

          // Process stats object to convert Decimal128 fields
          const stats = documentWithoutId.stats || { floorPrice: 0, totalVolume: 0 };

          // Use the recursive conversion function to handle all Decimal128 values
          const processedStats = this.convertDecimal128ToNumbers(stats);

          // Ensure floorPrice is a number
          if (typeof processedStats.floorPrice !== "number") {
            this.logger.warn(`floorPrice is still not a number after conversion for document ${id}. Setting to 0.`);
            processedStats.floorPrice = 0;
          }

          transformedDocument = {
            elasticsearchId: id.toString(),
            collectionAddress: documentWithoutId.collectionAddress?.toLowerCase() || documentWithoutId.collectionAddress?.toLowerCase(),
            name: documentWithoutId.name,
            slug: documentWithoutId.slug,
            logo: documentWithoutId.logo,
            isVerified: documentWithoutId.isVerified || false,
            totalSupply: totalSupply,
            stats: processedStats,
            status: documentWithoutId.status,
          };
        } else if (model === "userModel") {
          // Transform user data to match mapping
          transformedDocument = {
            elasticsearchId: id.toString(),
            address: documentWithoutId.address?.toLowerCase(),
            name: documentWithoutId.name,
            avatar: documentWithoutId.avatar,
            isVerified: documentWithoutId.isVerified || false,
            status: documentWithoutId.status,
          };
        } else if (model === "tokenModel") {
          // Transform token data to match mapping
          transformedDocument = {
            tokenId: documentWithoutId.tokenId,
            name: documentWithoutId.name,
            description: documentWithoutId.description,
            collectionAddress: documentWithoutId.collectionAddress?.toLowerCase(),
            collectionInfo: documentWithoutId.collectionInfo
              ? {
                  _id: documentWithoutId.collectionInfo._id?.toString(),
                  name: documentWithoutId.collectionInfo.name,
                  collectionAddress: documentWithoutId.collectionInfo.address?.toLowerCase() || documentWithoutId.collectionInfo.collectionAddress?.toLowerCase(),
                  isVerified: documentWithoutId.collectionInfo.isVerified || false,
                }
              : null,
            logo: documentWithoutId.logo,
            status: documentWithoutId.status,
            isCollectionBlacklisted: documentWithoutId.isCollectionBlacklisted || false,
            buyNow: documentWithoutId.buyNow || false,
          };
        }

        elasticsearchBatchData.push(transformedDocument);
      }

      return elasticsearchBatchData;
    } catch (error) {
      this.logger.error(error.message);
      return [];
    }
  }

  /**
   * Find data to be updated in Elasticsearch
   */
  async findDataToBeUpdatedInElasticsearch(model: string, indexName: string) {
    try {
      const elasticsearchBatchData = [];
      let selectData = {};
      let populateOptions = null;

      if (model === "userModel") {
        selectData = { _id: 1, name: 1, address: 1, isVerified: 1, avatar: 1, status: 1, elasticsearchId: 1 };
      } else if (model === "collectionModel") {
        selectData = { _id: 1, name: 1, slug: 1, logo: 1, stats: 1, totalSupply: 1, collectionAddress: 1, isVerified: 1, status: 1, elasticsearchId: 1 };
      } else if (model === "tokenModel") {
        selectData = {
          _id: 1,
          name: 1,
          logo: 1,
          buyNow: 1,
          collectionAddress: 1,
          tokenId: 1,
          description: 1,
          status: 1,
          elasticsearchId: 1,
          isCollectionBlacklisted: 1,
        };
        // Only populate for tokens since they need collection info
        populateOptions = { path: "collectionInfo", strictPopulate: false, select: ["_id", "name", "collectionAddress", "isVerified"] };
      }

      let query = this[model].find({ elasticsearchUpdate: true });

      if (populateOptions) {
        query = query.populate(populateOptions);
      }

      const dataToBeUpdated = await query.select(selectData).lean();

      for (let i = 0; i < dataToBeUpdated.length; i++) {
        elasticsearchBatchData.push({
          index: {
            _index: indexName,
            _id: dataToBeUpdated[i]._id.toString(),
          },
        });

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { _id: id, elasticsearchId: esId, ...documentWithoutId } = dataToBeUpdated[i];

        // Transform data based on model type to match mappings
        let transformedDocument = documentWithoutId;

        if (model === "collectionModel") {
          // Transform collection data to match mapping
          // Convert Decimal128 fields to numbers
          const totalSupply = documentWithoutId.totalSupply && documentWithoutId.totalSupply.$numberDecimal ? parseFloat(documentWithoutId.totalSupply.$numberDecimal) : 0;

          // Process stats object to convert Decimal128 fields
          const stats = documentWithoutId.stats || { floorPrice: 0, totalVolume: 0 };

          // Log the original stats object for debugging
          this.logger.debug(`Original stats for document ${id}: ${JSON.stringify(stats)}`);

          // Use the recursive conversion function to handle all Decimal128 values
          const processedStats = this.convertDecimal128ToNumbers(stats);

          // Log the processed stats for debugging
          this.logger.debug(`Processed stats for document ${id}: ${JSON.stringify(processedStats)}`);

          // Ensure floorPrice is a number
          if (typeof processedStats.floorPrice !== "number") {
            this.logger.warn(`floorPrice is still not a number after conversion for document ${id}. Setting to 0.`);
            processedStats.floorPrice = 0;
          }

          transformedDocument = {
            elasticsearchId: id.toString(),
            collectionAddress: documentWithoutId.collectionAddress?.toLowerCase() || documentWithoutId.collectionAddress?.toLowerCase(),
            name: documentWithoutId.name,
            slug: documentWithoutId.slug,
            logo: documentWithoutId.logo,
            isVerified: documentWithoutId.isVerified || false,
            totalSupply: totalSupply,
            stats: processedStats,
            status: documentWithoutId.status,
          };
        } else if (model === "userModel") {
          // Transform user data to match mapping
          transformedDocument = {
            elasticsearchId: id.toString(),
            address: documentWithoutId.address?.toLowerCase(),
            name: documentWithoutId.name,
            avatar: documentWithoutId.avatar,
            isVerified: documentWithoutId.isVerified || false,
            status: documentWithoutId.status,
          };
        } else if (model === "tokenModel") {
          // Transform token data to match mapping
          transformedDocument = {
            tokenId: documentWithoutId.tokenId,
            name: documentWithoutId.name,
            description: documentWithoutId.description,
            collectionAddress: documentWithoutId.collectionAddress?.toLowerCase(),
            collectionInfo: documentWithoutId.collectionInfo
              ? {
                  _id: documentWithoutId.collectionInfo._id?.toString(),
                  name: documentWithoutId.collectionInfo.name,
                  collectionAddress: documentWithoutId.collectionInfo.address?.toLowerCase() || documentWithoutId.collectionInfo.collectionAddress?.toLowerCase(),
                  isVerified: documentWithoutId.collectionInfo.isVerified || false,
                }
              : null,
            logo: documentWithoutId.logo,
            status: documentWithoutId.status,
            isCollectionBlacklisted: documentWithoutId.isCollectionBlacklisted || false,
            buyNow: documentWithoutId.buyNow || false,
          };
        }

        elasticsearchBatchData.push(transformedDocument);
      }

      return elasticsearchBatchData;
    } catch (error) {
      this.logger.error(error.message);
      return [];
    }
  }

  /**
   * Recursively convert all Decimal128 values to numbers
   * This handles nested objects and arrays
   */
  convertDecimal128ToNumbers(obj: any): any {
    // Handle null, undefined, or primitive values
    if (!obj || typeof obj !== "object") {
      return obj;
    }

    // Handle arrays
    if (Array.isArray(obj)) {
      return obj.map(item => this.convertDecimal128ToNumbers(item));
    }

    // Check if this is a Decimal128 object with $numberDecimal property
    if (obj.$numberDecimal !== undefined) {
      const result = parseFloat(obj.$numberDecimal);
      this.logger.debug(`Converted $numberDecimal ${obj.$numberDecimal} to ${result}`);
      return result;
    }

    // Handle the raw binary representation of Decimal128
    if (obj.bytes !== undefined) {
      this.logger.debug(`Found Decimal128 bytes representation: ${JSON.stringify(obj)}`);

      // Try to extract a default value from the collection schema
      // For floorPrice, we'll default to 0 which is better than sending an object
      return 0;
    }

    // Special case for stats object to ensure floorPrice is always a number
    if (obj.floorPrice !== undefined && typeof obj.floorPrice === "object") {
      this.logger.debug(`Found object in floorPrice field: ${JSON.stringify(obj.floorPrice)}`);
      // Force floorPrice to be 0 if it's an object
      const newObj = { ...obj };
      newObj.floorPrice = 0;
      return this.convertDecimal128ToNumbers(newObj);
    }

    // Special case for handling BSON Decimal128 objects directly
    // This is a more direct approach to handle Decimal128 objects
    if (obj.constructor && obj.constructor.name === "Decimal128") {
      try {
        // Try to convert to string and then to float
        const strValue = obj.toString();
        const numValue = parseFloat(strValue);
        this.logger.debug(`Converted Decimal128 object directly: ${strValue} to ${numValue}`);
        return numValue;
      } catch (error) {
        this.logger.error(`Error converting Decimal128 object: ${error.message}`);
        return 0;
      }
    }

    // Process regular objects
    const result = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        result[key] = this.convertDecimal128ToNumbers(obj[key]);
      }
    }
    return result;
  }

  /**
   * Bulk index data to Elasticsearch
   */
  async bulkIndexToElasticsearch(batchData: any[]) {
    try {
      const allBatchData = [...batchData[0], ...batchData[1], ...batchData[2]];

      if (allBatchData.length === 0) {
        this.logger.info("No data to index in Elasticsearch");
        return;
      }

      const maxBatchSize = 500; // Max number of operations per batch (reduced to prevent memory issues)
      const batchCount = Math.ceil(allBatchData.length / (maxBatchSize * 2)); // Divide by 2 because each document has 2 operations (index and document)

      for (let i = 0; i < batchCount; i++) {
        const fromIndex = i * maxBatchSize * 2;
        const toIndex = Math.min((i + 1) * maxBatchSize * 2, allBatchData.length);

        const batch = allBatchData.slice(fromIndex, toIndex);

        if (batch.length > 0) {
          const result = await this.elasticsearchService.bulkIndex(batch);

          if (result.errors) {
            this.logger.error("Elasticsearch bulk indexing had errors");

            // Log detailed error information for debugging
            const errorItems = result.items.filter(item => item.index && item.index.error);

            errorItems.forEach(item => {
              const error = item.index.error;
              const docId = item.index._id;
              const reason = error.reason || "Unknown error";
              const causedBy = error.caused_by ? error.caused_by.reason : "";

              this.logger.error(`Error indexing document ${docId}: ${reason}. Caused by: ${causedBy}`);

              // Log the document that caused the error for debugging
              const docIndex = batch.findIndex(op => op.index && op.index._id === docId);

              if (docIndex >= 0 && docIndex + 1 < batch.length) {
                const doc = batch[docIndex + 1];
                console.log(`Problematic document: ${JSON.stringify(doc)}`);
              }
            });
          } else {
            this.logger.info(`Indexed ${batch.length / 2} documents to Elasticsearch`);
          }

          // Update MongoDB documents with Elasticsearch IDs
          await this.updateElasticsearchIdsInDB(batch);

          // Add a small delay between batches to prevent overwhelming Elasticsearch
          if (i < batchCount - 1) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        }
      }
    } catch (error) {
      this.logger.error(`Error in bulk indexing: ${error.message}`);
    }
  }

  /**
   * Update Elasticsearch IDs in MongoDB
   */
  async updateElasticsearchIdsInDB(batchData: any[]) {
    try {
      const updatePromises = [];

      for (let i = 0; i < batchData.length; i += 2) {
        // Skip every other item (the document data)
        const indexOperation = batchData[i];
        const documentId = indexOperation.index._id;
        const indexName = indexOperation.index._index;

        let modelName = "";
        if (indexName.includes("_users")) {
          modelName = "userModel";
        } else if (indexName.includes("_collections")) {
          modelName = "collectionModel";
        } else if (indexName.includes("_tokens")) {
          modelName = "tokenModel";
        }

        if (modelName) {
          updatePromises.push(this.updateElasticsearchId(documentId, documentId, modelName));
        }
      }

      this.logger.info(`Updating ${updatePromises.length} documents in MongoDB with Elasticsearch IDs`);
      await Promise.all(updatePromises);
    } catch (error) {
      this.logger.error(`Error updating Elasticsearch IDs in MongoDB: ${error.message}`);
    }
  }

  /**
   * Update Elasticsearch ID for a document
   */
  async updateElasticsearchId(id: string, elasticsearchId: string, model: string) {
    try {
      return await this[model].findByIdAndUpdate(id, {
        elasticsearchId,
        elasticsearchUpdate: false,
      });
    } catch (error) {
      this.logger.error(`Failed to update elasticsearchId, error: ${error.message}, ${model}+id: ${id}, elasticsearchId: ${elasticsearchId}`);
    }
  }
}
