import { ElasticsearchCronService } from "./elasticsearch-cron.service";
import { Collections, CollectionsSchema } from "../../database/schemas/collections.schema";
import { Tokens, TokensSchema } from "../../database/schemas/tokens.schema";
import { Users, UsersSchema } from "../../database/schemas/users.schema";
import { ElasticsearchModule } from "../elasticsearch/elasticsearch.module";
import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Collections.name, schema: CollectionsSchema },
      { name: Tokens.name, schema: TokensSchema },
      { name: Users.name, schema: UsersSchema },
    ]),
    ElasticsearchModule,
  ],
  providers: [ElasticsearchCronService],
})
export class ElasticsearchCronModule {}
