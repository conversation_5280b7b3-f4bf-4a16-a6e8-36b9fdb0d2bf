import { AppModule } from "./app.module";
import { LoggerService } from "./shared/Providers/logger.service";
import configuration from "./shared/config/app.config";

import { ConfigService } from "@nestjs/config";
import { NestFactory } from "@nestjs/core";
import { INestApplication } from "@nestjs/common";

let app: INestApplication;

async function bootstrap() {
  app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const logger = app.get(LoggerService);

  // Enable graceful shutdown
  app.enableShutdownHooks();

  const port = configService.get<number>("PORT");
  app.use("/live", (req: any, res: any) => {
    res.status(200).send("Server is up and running");
  });

  await app.listen(port);
  logger.info(`Server running on http://localhost:${port}`);

  // Setup graceful shutdown handlers
  const gracefulShutdown = async (signal: string) => {
    logger.info(`Received ${signal}, starting graceful shutdown...`);

    try {
      await app.close();
      logger.info("Application closed successfully");
      process.exit(0);
    } catch (error) {
      logger.error("Error during shutdown:", error);
      process.exit(1);
    }
  };

  // Register signal handlers
  process.on("SIGTERM", () => {
    void gracefulShutdown("SIGTERM");
  });
  process.on("SIGINT", () => {
    void gracefulShutdown("SIGINT");
  });

  // Handle uncaught exceptions
  process.on("uncaughtException", error => {
    logger.error("Uncaught Exception:", error);
    void gracefulShutdown("uncaughtException");
  });

  // Handle unhandled promise rejections
  process.on("unhandledRejection", (reason, promise) => {
    logger.error(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
    void gracefulShutdown("unhandledRejection");
  });
}

void configuration().then(async () => {
  await bootstrap();
});
