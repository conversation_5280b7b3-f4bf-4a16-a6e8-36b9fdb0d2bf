# Use official Node.js 20 LTS base image
FROM node:20-alpine AS builder

# Add dependencies for building native modules
RUN apk add --no-cache python3 make g++ 

# Set working directory
WORKDIR /app

# Copy only essential files for dependency install
COPY package*.json ./
COPY tsconfig.* ./

# Install dependencies (use npm ci if package-lock.json exists)
RUN npm install

# Copy the rest of the application code
COPY . .

# Build the application
RUN npm run build -- --config tsconfig.build.json


# Use a smaller image for production (distroless or alpine)
FROM node:20-alpine

WORKDIR /app

# Copy built app and node_modules from builder stage
COPY --from=builder /app /app

# Increase memory limit
ENV NODE_OPTIONS=--max-old-space-size=8192

# Expose port (optional if needed by infra)
# EXPOSE 3000

# Start the application
CMD ["npm", "run", "start:prod"]
